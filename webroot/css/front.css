body {
    overflow-x: hidden;
    font-family: "Roboto Slab", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.text-muted {
    color: #777777;
}

.text-primary {
    color: #fed136;
}

p {
    font-size: 14px;
    line-height: 1.75;
}

p.large {
    font-size: 16px;
}

a,
a:hover,
a:focus,
a:active,
a.active {
    outline: none;
}

a {
    color: #fed136;
}

a:hover,
a:focus,
a:active,
a.active {
    color: #fec503;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    font-weight: 700;
}

.img-centered {
    margin: 0 auto;
}

.bg-light-gray {
    background-color: #eeeeee;
}

.bg-darkest-gray {
    background-color: #222222;
}

.btn-primary {
    color: white;
    background-color: #fed136;
    border-color: #fed136;
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    font-weight: 700;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
    color: white;
    background-color: #fec503;
    border-color: #f6bf01;
}

.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
    background-color: #fed136;
    border-color: #fed136;
}

.btn-primary .badge {
    color: #fed136;
    background-color: white;
}

.btn-xl {
    color: white;
    background-color: #fed136;
    border-color: #fed136;
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    font-weight: 700;
    border-radius: 3px;
    font-size: 18px;
    padding: 20px 40px;
}

.btn-xl:hover,
.btn-xl:focus,
.btn-xl:active,
.btn-xl.active,
.open .dropdown-toggle.btn-xl {
    color: white;
    background-color: #fec503;
    border-color: #f6bf01;
}

.btn-xl:active,
.btn-xl.active,
.open .dropdown-toggle.btn-xl {
    background-image: none;
}

.btn-xl.disabled,
.btn-xl[disabled],
fieldset[disabled] .btn-xl,
.btn-xl.disabled:hover,
.btn-xl[disabled]:hover,
fieldset[disabled] .btn-xl:hover,
.btn-xl.disabled:focus,
.btn-xl[disabled]:focus,
fieldset[disabled] .btn-xl:focus,
.btn-xl.disabled:active,
.btn-xl[disabled]:active,
fieldset[disabled] .btn-xl:active,
.btn-xl.disabled.active,
.btn-xl[disabled].active,
fieldset[disabled] .btn-xl.active {
    background-color: #fed136;
    border-color: #fed136;
}

.btn-xl .badge {
    color: #fed136;
    background-color: white;
}

.navbar-default {
    background-color: #222222;
    border-color: transparent;
}

.navbar-default .navbar-brand {
    color: #fed136;
    font-family: "Kaushan Script", "Helvetica Neue", Helvetica, Arial, cursive;
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus,
.navbar-default .navbar-brand:active,
.navbar-default .navbar-brand.active {
    color: #fec503;
}

.navbar-default .navbar-collapse {
    border-color: rgba(255, 255, 255, 0.02);
}

.navbar-default .navbar-toggle {
    background-color: #fed136;
    border-color: #fed136;
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: white;
}

.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
    background-color: #fed136;
}

.navbar-default .nav li a {
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 1px;
    color: white;
}

.navbar-default .nav li a:hover,
.navbar-default .nav li a:focus {
    color: #fed136;
    outline: none;
}

.navbar-default .navbar-nav > .active > a {
    border-radius: 0;
    color: white;
    background-color: #fed136;
}

.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: white;
    background-color: #fec503;
}

.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:focus,
.navbar-default .navbar-nav > .open > a:hover {
    color: #ffffff;
    background-color: transparent;
}

.dropdown-menu {
    padding: 0;
    min-width: 175px;
}

.navbar-default .nav .dropdown-menu > li > a {
    font-weight: 700;
    font-size: 11px;
    padding: 8px 10px;
    color: #505050;
    border-bottom: 1px solid #dfdfdf;
}

@media (min-width: 768px) {
    .navbar-default {
        background-color: transparent;
        padding: 25px 0;
        -webkit-transition: padding 0.3s;
        -moz-transition: padding 0.3s;
        transition: padding 0.3s;
        border: none;
    }

    .navbar-default .navbar-brand {
        font-size: 2em;
        -webkit-transition: all 0.3s;
        -moz-transition: all 0.3s;
        transition: all 0.3s;
    }

    .navbar-default .navbar-nav > .active > a {
        border-radius: 3px;
    }

    .navbar-default .navbar-nav > li.language-selector > a,
    .navbar-default.affix .navbar-nav > li.language-selector > a {
        font-size: 21px;
    }
}

@media (min-width: 768px) {
    .navbar-default.affix {
        background-color: #222222;
        padding: 10px 0;
    }

    .navbar-default.affix .navbar-brand {
        font-size: 1.5em;
    }
}

header {
    background-image: url(../img/header.jpg);
    background-repeat: no-repeat;
    background-attachment: scroll;
    background-position: center center;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    background-size: cover;
    -o-background-size: cover;
    text-align: center;
    color: white;
}

header .intro-text {
    padding-top: 100px;
    padding-bottom: 50px;
}

header .intro-text .intro-lead-in {
    font-family: "Droid Serif", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-style: italic;
    font-size: 22px;
    line-height: 22px;
    margin-bottom: 25px;
}

header .intro-text .intro-heading {
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 50px;
    line-height: 50px;
    margin-bottom: 25px;
}

@media (min-width: 768px) {
    header .intro-text {
        padding-top: 200px;
        padding-bottom: 200px;
    }

    header .intro-text .intro-lead-in {
        font-family: "Droid Serif", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-style: italic;
        font-size: 40px;
        line-height: 40px;
        margin-bottom: 25px;
    }

    header .intro-text .intro-heading {
        font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
        text-transform: uppercase;
        font-weight: 700;
        font-size: 75px;
        line-height: 75px;
        margin-bottom: 50px;
    }
}

section {
    padding: 100px 0;
}

section h2.section-heading {
    font-size: 40px;
    margin-top: 0;
    margin-bottom: 15px;
}

section h3.section-subheading {
    font-size: 16px;
    font-family: "Droid Serif", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-style: italic;
    font-weight: 400;
    margin-bottom: 75px;
}

@media (min-width: 768px) {
    section {
        padding: 150px 0;
    }
}

.service-heading {
    margin: 15px 0;
    text-transform: none;
}

section#contact {
    background-color: #222222;
    background-image: url('../img/map-image.png');
    background-position: center;
    background-repeat: no-repeat;
    color: #ffffff;
}

section#contact .section-heading {
    color: white;
}

section#contact .form-group {
    margin-bottom: 25px;
}

section#contact .form-group input,
section#contact .form-group textarea {
    padding: 20px;
}

section#contact .form-group input.form-control {
    height: auto;
}

section#contact .form-group textarea.form-control {
    height: 236px;
}

section#contact .form-control:focus {
    border-color: #fed136;
    box-shadow: none;
}

section#contact .text-danger {
    color: #e74c3c;
}

footer {
    border-top: 1px #222222 solid;
    padding: 25px 0;
    text-align: center;
}

footer span.copyright {
    line-height: 40px;
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    text-transform: none;
}

footer ul.quicklinks {
    margin-bottom: 0;
    line-height: 40px;
    font-family: "Montserrat", "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    text-transform: none;
}

ul.social-buttons {
    margin-bottom: 0;
}

ul.social-buttons li a {
    display: block;
    background-color: #222222;
    height: 40px;
    width: 40px;
    border-radius: 100%;
    font-size: 20px;
    line-height: 40px;
    color: white;
    outline: none;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

ul.social-buttons li a:hover,
ul.social-buttons li a:focus,
ul.social-buttons li a:active {
    background-color: #fed136;
}

.btn:focus,
.btn:active,
.btn.active,
.btn:active:focus {
    outline: none;
}
