/*!Developer - <EMAIL> (Yu idea designs)
 * Bootstrap.CLDT v3.3.7 (http://getbootstrap.com) Dark-theme
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE) */

:root {
	--blue: #007bff;
	--indigo: #6610f2;
	--purple: #6f42c1;
	--pink: #e83e8c;
	--red: #dc3545;
	--orange: #fd7e14;
	--yellow: #ffc107;
	--green: #28a745;
	--teal: #20c997;
	--cyan: #17a2b8;
	--white: #fff;
	--gray: #6c757d;
	--gray-dark: #343a40;
	--primary: #007bff;
	--secondary: #6c757d;
	--success: #28a745;
	--info: #17a2b8;
	--warning: #ffc107;
	--danger: #dc3545;
	--light: #f8f9fa;
	--dark: #343a40;
	--breakpoint-xs: 0;
	--breakpoint-sm: 576px;
	--breakpoint-md: 768px;
	--breakpoint-lg: 992px;
	--breakpoint-xl: 1200px;
	--font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	--font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

*,
::after,
::before {
	box-sizing: border-box
}

html {
	font-family: Montserrat;
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	-ms-overflow-style: scrollbar;
	-webkit-tap-highlight-color: transparent
}

body {
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	text-align: left;
	background-color: #fff;
	color: #fff;
	font-family: Montserrat!important;
	font-size: 14px;
	background: #121d33
}

header .section-inner {
	background-image: url(../img/cloud.png)!important;
	background-repeat: repeat-x;
	background-attachment: scroll;
	background-position: bottom center;
	background-size: contain;
	margin-bottom: 30px
}

.main-logo {
	height: 55px;
	display: inline-block
}

.main-logo img {
	max-height: 100px
}

.btn-mn {
	background-color: #00ea8a;
	box-shadow: none;
	color: #fff;
	margin: 4px 1px;
	font-weight: 500;
	padding: 11px 30px;
	font-size: .875rem;
	line-height: 1.35em;
	border-width: 1px;
	border-radius: 30px;
	border: none;
	margin-top: 20px
}

.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-xs-1,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9 {
	position: relative;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px
}

.col-xs-1,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9 {
	float: left
}

.col-xs-12 {
	width: 100%
}

.col-xs-11 {
	width: 91.66666667%
}

.col-xs-10 {
	width: 83.33333333%
}

.col-xs-9 {
	width: 75%
}

.col-xs-8 {
	width: 66.66666667%
}

.col-xs-7 {
	width: 58.33333333%
}

.col-xs-6 {
	width: 50%
}

.col-xs-5 {
	width: 41.66666667%
}

.col-xs-4 {
	width: 33.33333333%
}

.col-xs-3 {
	width: 25%
}

.col-xs-2 {
	width: 16.66666667%
}

.col-xs-1 {
	width: 8.33333333%
}

.col-xs-pull-12 {
	right: 100%
}

.col-xs-pull-11 {
	right: 91.66666667%
}

.col-xs-pull-10 {
	right: 83.33333333%
}

.col-xs-pull-9 {
	right: 75%
}

.col-xs-pull-8 {
	right: 66.66666667%
}

.col-xs-pull-7 {
	right: 58.33333333%
}

.col-xs-pull-6 {
	right: 50%
}

.col-xs-pull-5 {
	right: 41.66666667%
}

.col-xs-pull-4 {
	right: 33.33333333%
}

.col-xs-pull-3 {
	right: 25%
}

.col-xs-pull-2 {
	right: 16.66666667%
}

.col-xs-pull-1 {
	right: 8.33333333%
}

.col-xs-pull-0 {
	right: auto
}

.col-xs-push-12 {
	left: 100%
}

.col-xs-push-11 {
	left: 91.66666667%
}

.col-xs-push-10 {
	left: 83.33333333%
}

.col-xs-push-9 {
	left: 75%
}

.col-xs-push-8 {
	left: 66.66666667%
}

.col-xs-push-7 {
	left: 58.33333333%
}

.col-xs-push-6 {
	left: 50%
}

.col-xs-push-5 {
	left: 41.66666667%
}

.col-xs-push-4 {
	left: 33.33333333%
}

.col-xs-push-3 {
	left: 25%
}

.col-xs-push-2 {
	left: 16.66666667%
}

.col-xs-push-1 {
	left: 8.33333333%
}

.col-xs-push-0 {
	left: auto
}

.col-xs-offset-12 {
	margin-left: 100%
}

.col-xs-offset-11 {
	margin-left: 91.66666667%
}

.col-xs-offset-10 {
	margin-left: 83.33333333%
}

.col-xs-offset-9 {
	margin-left: 75%
}

.col-xs-offset-8 {
	margin-left: 66.66666667%
}

.col-xs-offset-7 {
	margin-left: 58.33333333%
}

.col-xs-offset-6 {
	margin-left: 50%
}

.col-xs-offset-5 {
	margin-left: 41.66666667%
}

.col-xs-offset-4 {
	margin-left: 33.33333333%
}

.col-xs-offset-3 {
	margin-left: 25%
}

.col-xs-offset-2 {
	margin-left: 16.66666667%
}

.col-xs-offset-1 {
	margin-left: 8.33333333%
}

.col-xs-offset-0 {
	margin-left: 0
}

@media (min-width:768px) {
	.col-sm-1,
	.col-sm-10,
	.col-sm-11,
	.col-sm-12,
	.col-sm-2,
	.col-sm-3,
	.col-sm-4,
	.col-sm-5,
	.col-sm-6,
	.col-sm-7,
	.col-sm-8,
	.col-sm-9 {
		float: left
	}
	.col-sm-12 {
		width: 100%
	}
	.col-sm-11 {
		width: 91.66666667%
	}
	.col-sm-10 {
		width: 83.33333333%
	}
	.col-sm-9 {
		width: 75%
	}
	.col-sm-8 {
		width: 66.66666667%
	}
	.col-sm-7 {
		width: 58.33333333%
	}
	.col-sm-6 {
		width: 50%
	}
	.col-sm-5 {
		width: 41.66666667%
	}
	.col-sm-4 {
		width: 33.33333333%
	}
	.col-sm-3 {
		width: 25%
	}
	.col-sm-2 {
		width: 16.66666667%
	}
	.col-sm-1 {
		width: 8.33333333%
	}
	.col-sm-pull-12 {
		right: 100%
	}
	.col-sm-pull-11 {
		right: 91.66666667%
	}
	.col-sm-pull-10 {
		right: 83.33333333%
	}
	.col-sm-pull-9 {
		right: 75%
	}
	.col-sm-pull-8 {
		right: 66.66666667%
	}
	.col-sm-pull-7 {
		right: 58.33333333%
	}
	.col-sm-pull-6 {
		right: 50%
	}
	.col-sm-pull-5 {
		right: 41.66666667%
	}
	.col-sm-pull-4 {
		right: 33.33333333%
	}
	.col-sm-pull-3 {
		right: 25%
	}
	.col-sm-pull-2 {
		right: 16.66666667%
	}
	.col-sm-pull-1 {
		right: 8.33333333%
	}
	.col-sm-pull-0 {
		right: auto
	}
	.col-sm-push-12 {
		left: 100%
	}
	.col-sm-push-11 {
		left: 91.66666667%
	}
	.col-sm-push-10 {
		left: 83.33333333%
	}
	.col-sm-push-9 {
		left: 75%
	}
	.col-sm-push-8 {
		left: 66.66666667%
	}
	.col-sm-push-7 {
		left: 58.33333333%
	}
	.col-sm-push-6 {
		left: 50%
	}
	.col-sm-push-5 {
		left: 41.66666667%
	}
	.col-sm-push-4 {
		left: 33.33333333%
	}
	.col-sm-push-3 {
		left: 25%
	}
	.col-sm-push-2 {
		left: 16.66666667%
	}
	.col-sm-push-1 {
		left: 8.33333333%
	}
	.col-sm-push-0 {
		left: auto
	}
	.col-sm-offset-12 {
		margin-left: 100%
	}
	.col-sm-offset-11 {
		margin-left: 91.66666667%
	}
	.col-sm-offset-10 {
		margin-left: 83.33333333%
	}
	.col-sm-offset-9 {
		margin-left: 75%
	}
	.col-sm-offset-8 {
		margin-left: 66.66666667%
	}
	.col-sm-offset-7 {
		margin-left: 58.33333333%
	}
	.col-sm-offset-6 {
		margin-left: 50%
	}
	.col-sm-offset-5 {
		margin-left: 41.66666667%
	}
	.col-sm-offset-4 {
		margin-left: 33.33333333%
	}
	.col-sm-offset-3 {
		margin-left: 25%
	}
	.col-sm-offset-2 {
		margin-left: 16.66666667%
	}
	.col-sm-offset-1 {
		margin-left: 8.33333333%
	}
	.col-sm-offset-0 {
		margin-left: 0
	}
}

@media (min-width:992px) {
	.col-md-1,
	.col-md-10,
	.col-md-11,
	.col-md-12,
	.col-md-2,
	.col-md-3,
	.col-md-4,
	.col-md-5,
	.col-md-6,
	.col-md-7,
	.col-md-8,
	.col-md-9 {
		float: left
	}
	.col-md-12 {
		width: 100%
	}
	.col-md-11 {
		width: 91.66666667%
	}
	.col-md-10 {
		width: 83.33333333%
	}
	.col-md-9 {
		width: 75%
	}
	.col-md-8 {
		width: 66.66666667%
	}
	.col-md-7 {
		width: 58.33333333%
	}
	.col-md-6 {
		width: 50%
	}
	.col-md-5 {
		width: 50%
	}
	.col-md-4 {
		width: 33.33333333%
	}
	.col-md-3 {
		width: 25%
	}
	.col-md-2 {
		width: 16.66666667%
	}
	.col-md-1 {
		width: 8.33333333%
	}
	.col-md-pull-12 {
		right: 100%
	}
	.col-md-pull-11 {
		right: 91.66666667%
	}
	.col-md-pull-10 {
		right: 83.33333333%
	}
	.col-md-pull-9 {
		right: 75%
	}
	.col-md-pull-8 {
		right: 66.66666667%
	}
	.col-md-pull-7 {
		right: 58.33333333%
	}
	.col-md-pull-6 {
		right: 50%
	}
	.col-md-pull-5 {
		right: 41.66666667%
	}
	.col-md-pull-4 {
		right: 33.33333333%
	}
	.col-md-pull-3 {
		right: 25%
	}
	.col-md-pull-2 {
		right: 16.66666667%
	}
	.col-md-pull-1 {
		right: 8.33333333%
	}
	.col-md-pull-0 {
		right: auto
	}
	.col-md-push-12 {
		left: 100%
	}
	.col-md-push-11 {
		left: 91.66666667%
	}
	.col-md-push-10 {
		left: 83.33333333%
	}
	.col-md-push-9 {
		left: 75%
	}
	.col-md-push-8 {
		left: 66.66666667%
	}
	.col-md-push-7 {
		left: 58.33333333%
	}
	.col-md-push-6 {
		left: 50%
	}
	.col-md-push-5 {
		left: 41.66666667%
	}
	.col-md-push-4 {
		left: 33.33333333%
	}
	.col-md-push-3 {
		left: 25%
	}
	.col-md-push-2 {
		left: 16.66666667%
	}
	.col-md-push-1 {
		left: 8.33333333%
	}
	.col-md-push-0 {
		left: auto
	}
	.col-md-offset-12 {
		margin-left: 100%
	}
	.col-md-offset-11 {
		margin-left: 91.66666667%
	}
	.col-md-offset-10 {
		margin-left: 83.33333333%
	}
	.col-md-offset-9 {
		margin-left: 75%
	}
	.col-md-offset-8 {
		margin-left: 66.66666667%
	}
	.col-md-offset-7 {
		margin-left: 58.33333333%
	}
	.col-md-offset-6 {
		margin-left: 50%
	}
	.col-md-offset-5 {
		margin-left: 41.66666667%
	}
	.col-md-offset-4 {
		margin-left: 33.33333333%
	}
	.col-md-offset-3 {
		margin-left: 25%
	}
	.col-md-offset-2 {
		margin-left: 16.66666667%
	}
	.col-md-offset-1 {
		margin-left: 8.33333333%
	}
	.col-md-offset-0 {
		margin-left: 0
	}
}

@media (min-width:1200px) {
	.col-lg-1,
	.col-lg-10,
	.col-lg-11,
	.col-lg-12,
	.col-lg-2,
	.col-lg-3,
	.col-lg-4,
	.col-lg-5,
	.col-lg-6,
	.col-lg-7,
	.col-lg-8,
	.col-lg-9 {
		float: left
	}
	.col-lg-12 {
		width: 100%
	}
	.col-lg-11 {
		width: 91.66666667%
	}
	.col-lg-10 {
		width: 83.33333333%
	}
	.col-lg-9 {
		width: 75%
	}
	.col-lg-8 {
		width: 66.66666667%
	}
	.col-lg-7 {
		width: 58.33333333%
	}
	.col-lg-6 {
		width: 50%
	}
	.col-lg-5 {
		width: 41.66666667%
	}
	.col-lg-4 {
		width: 33.33333333%
	}
	.col-lg-3 {
		width: 25%
	}
	.col-lg-2 {
		width: 16.66666667%
	}
	.col-lg-1 {
		width: 8.33333333%
	}
	.col-lg-pull-12 {
		right: 100%
	}
	.col-lg-pull-11 {
		right: 91.66666667%
	}
	.col-lg-pull-10 {
		right: 83.33333333%
	}
	.col-lg-pull-9 {
		right: 75%
	}
	.col-lg-pull-8 {
		right: 66.66666667%
	}
	.col-lg-pull-7 {
		right: 58.33333333%
	}
	.col-lg-pull-6 {
		right: 50%
	}
	.col-lg-pull-5 {
		right: 41.66666667%
	}
	.col-lg-pull-4 {
		right: 33.33333333%
	}
	.col-lg-pull-3 {
		right: 25%
	}
	.col-lg-pull-2 {
		right: 16.66666667%
	}
	.col-lg-pull-1 {
		right: 8.33333333%
	}
	.col-lg-pull-0 {
		right: auto
	}
	.col-lg-push-12 {
		left: 100%
	}
	.col-lg-push-11 {
		left: 91.66666667%
	}
	.col-lg-push-10 {
		left: 83.33333333%
	}
	.col-lg-push-9 {
		left: 75%
	}
	.col-lg-push-8 {
		left: 66.66666667%
	}
	.col-lg-push-7 {
		left: 58.33333333%
	}
	.col-lg-push-6 {
		left: 50%
	}
	.col-lg-push-5 {
		left: 41.66666667%
	}
	.col-lg-push-4 {
		left: 33.33333333%
	}
	.col-lg-push-3 {
		left: 25%
	}
	.col-lg-push-2 {
		left: 16.66666667%
	}
	.col-lg-push-1 {
		left: 8.33333333%
	}
	.col-lg-push-0 {
		left: auto
	}
	.col-lg-offset-12 {
		margin-left: 100%
	}
	.col-lg-offset-11 {
		margin-left: 91.66666667%
	}
	.col-lg-offset-10 {
		margin-left: 83.33333333%
	}
	.col-lg-offset-9 {
		margin-left: 75%
	}
	.col-lg-offset-8 {
		margin-left: 66.66666667%
	}
	.col-lg-offset-7 {
		margin-left: 58.33333333%
	}
	.col-lg-offset-6 {
		margin-left: 50%
	}
	.col-lg-offset-5 {
		margin-left: 41.66666667%
	}
	.col-lg-offset-4 {
		margin-left: 33.33333333%
	}
	.col-lg-offset-3 {
		margin-left: 25%
	}
	.col-lg-offset-2 {
		margin-left: 16.66666667%
	}
	.col-lg-offset-1 {
		margin-left: 8.33333333%
	}
	.col-lg-offset-0 {
		margin-left: 0
	}
}

.bg-animation {
	position: absolute;
	opacity: 1;
	left: 0;
	top: -20%;
	width: 100%;
	height: 840px;
	background-image: none;
	background-repeat: no-repeat;
	/*! background-size:100% 100%; */
	-webkit-transform: skewY(-4deg);
	-moz-transform: skewY(-4deg);
	-ms-transform: skewY(-4deg);
	transform: skewY(-4deg);
	z-index: -1;
	background-color: #130f40;
	background-image: linear-gradient(to left top, #162239, #232546, #362651, #4d2557, #651f57)
}

.alert {
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px
}

.alert h4 {
	margin-top: 0;
	color: inherit
}

.alert .alert-link {
	font-weight: 700
}

.alert>p,
.alert>ul {
	margin-bottom: 0
}

.alert>p+p {
	margin-top: 5px
}

.alert-dismissable,
.alert-dismissible {
	padding-right: 35px
}

.alert-dismissable .close,
.alert-dismissible .close {
	position: relative;
	top: -2px;
	right: -21px;
	color: inherit
}

.alert-success {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6
}

.alert-success hr {
	border-top-color: #c9e2b3
}

.alert-success .alert-link {
	color: #2b542c
}

.alert-info {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1
}

.alert-info hr {
	border-top-color: #a6e1ec
}

.alert-info .alert-link {
	color: #245269
}

.alert-warning {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc
}

.alert-warning hr {
	border-top-color: #f7e1b5
}

.alert-warning .alert-link {
	color: #66512c
}

.alert-danger {
	color: #ff3430;
	background-color: #162239;
	border-color: #162239
}

.alert-danger hr {
	border-top-color: #e4b9c0
}

.alert-danger .alert-link {
	color: #843534
}

@-webkit-keyframes progress-bar-stripes {
	from {
		background-position: 40px 0
	}
	to {
		background-position: 0 0
	}
}

@-o-keyframes progress-bar-stripes {
	from {
		background-position: 40px 0
	}
	to {
		background-position: 0 0
	}
}

@keyframes progress-bar-stripes {
	from {
		background-position: 40px 0
	}
	to {
		background-position: 0 0
	}
}

.progress {
	height: 20px;
	margin-bottom: 20px;
	overflow: hidden;
	background-color: #f5f5f5;
	border-radius: 4px;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1)
}

.progress-bar {
	float: left;
	width: 0;
	height: 100%;
	font-size: 12px;
	line-height: 20px;
	color: #fff;
	text-align: center;
	background-color: #337ab7;
	box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
	transition: width .6s ease
}

.progress-bar-striped,
.progress-striped .progress-bar {
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-size: 40px 40px
}

.progress-bar.active,
.progress.active .progress-bar {
	animation: progress-bar-stripes 2s linear infinite
}

.progress-bar-success {
	background-color: #5cb85c
}

.progress-striped .progress-bar-success {
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-info {
	background-color: #5bc0de
}

.progress-striped .progress-bar-info {
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-warning {
	background-color: #f0ad4e
}

.progress-striped .progress-bar-warning {
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-danger {
	background-color: #d9534f
}

.progress-striped .progress-bar-danger {
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.media {
	margin-top: 15px
}

.media:first-child {
	margin-top: 0
}

.media,
.media-body {
	overflow: hidden
}

.media-body {
	width: 10000px
}

.media-object {
	display: block
}

.media-object.img-thumbnail {
	max-width: none
}

.media-right,
.media>.pull-right {
	padding-left: 10px
}

.media-left,
.media>.pull-left {
	padding-right: 10px
}

.media-body,
.media-left,
.media-right {
	display: table-cell;
	vertical-align: top
}

.media-middle {
	vertical-align: middle
}

.media-bottom {
	vertical-align: bottom
}

.media-heading {
	margin-top: 0;
	margin-bottom: 5px
}

.media-list {
	padding-left: 0;
	list-style: none
}

.list-group {
	padding-left: 0;
	margin-bottom: 20px
}

.list-group-item {
	position: relative;
	display: block;
	padding: 10px 15px;
	margin-bottom: -1px;
	background-color: #fff;
	border: 1px solid #ddd
}

.list-group-item:first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px
}

.list-group-item:last-child {
	margin-bottom: 0;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px
}

a.list-group-item,
button.list-group-item {
	color: #555
}

a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
	color: #333
}

a.list-group-item:focus,
a.list-group-item:hover,
button.list-group-item:focus,
button.list-group-item:hover {
	color: #555;
	text-decoration: none;
	background-color: #f5f5f5
}

button.list-group-item {
	width: 100%;
	text-align: left
}

.list-group-item.disabled,
.list-group-item.disabled:focus,
.list-group-item.disabled:hover {
	color: #777;
	cursor: not-allowed;
	background-color: #eee
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading {
	color: inherit
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text {
	color: #777
}

.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
	z-index: 2;
	color: #fff;
	background-color: #337ab7;
	border-color: #337ab7
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading>.small,
.list-group-item.active .list-group-item-heading>small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading>.small,
.list-group-item.active:focus .list-group-item-heading>small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading>.small,
.list-group-item.active:hover .list-group-item-heading>small {
	color: inherit
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:focus .list-group-item-text,
.list-group-item.active:hover .list-group-item-text {
	color: #c7ddef
}

.list-group-item-success {
	color: #3c763d;
	background-color: #dff0d8
}

a.list-group-item-success,
button.list-group-item-success {
	color: #3c763d
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
	color: inherit
}

a.list-group-item-success:focus,
a.list-group-item-success:hover,
button.list-group-item-success:focus,
button.list-group-item-success:hover {
	color: #3c763d;
	background-color: #d0e9c6
}

a.list-group-item-success.active,
a.list-group-item-success.active:focus,
a.list-group-item-success.active:hover,
button.list-group-item-success.active,
button.list-group-item-success.active:focus,
button.list-group-item-success.active:hover {
	color: #fff;
	background-color: #3c763d;
	border-color: #3c763d
}

.list-group-item-info {
	color: #31708f;
	background-color: #d9edf7
}

a.list-group-item-info,
button.list-group-item-info {
	color: #31708f
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
	color: inherit
}

a.list-group-item-info:focus,
a.list-group-item-info:hover,
button.list-group-item-info:focus,
button.list-group-item-info:hover {
	color: #31708f;
	background-color: #c4e3f3
}

a.list-group-item-info.active,
a.list-group-item-info.active:focus,
a.list-group-item-info.active:hover,
button.list-group-item-info.active,
button.list-group-item-info.active:focus,
button.list-group-item-info.active:hover {
	color: #fff;
	background-color: #31708f;
	border-color: #31708f
}

.list-group-item-warning {
	color: #8a6d3b;
	background-color: #fcf8e3
}

a.list-group-item-warning,
button.list-group-item-warning {
	color: #8a6d3b
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
	color: inherit
}

a.list-group-item-warning:focus,
a.list-group-item-warning:hover,
button.list-group-item-warning:focus,
button.list-group-item-warning:hover {
	color: #8a6d3b;
	background-color: #faf2cc
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:focus,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active,
button.list-group-item-warning.active:focus,
button.list-group-item-warning.active:hover {
	color: #fff;
	background-color: #8a6d3b;
	border-color: #8a6d3b
}

.list-group-item-danger {
	color: #a94442;
	background-color: #f2dede
}

a.list-group-item-danger,
button.list-group-item-danger {
	color: #a94442
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
	color: inherit
}

a.list-group-item-danger:focus,
a.list-group-item-danger:hover,
button.list-group-item-danger:focus,
button.list-group-item-danger:hover {
	color: #a94442;
	background-color: #ebcccc
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:focus,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active,
button.list-group-item-danger.active:focus,
button.list-group-item-danger.active:hover {
	color: #fff;
	background-color: #a94442;
	border-color: #a94442
}

.list-group-item-heading {
	margin-top: 0;
	margin-bottom: 5px
}

.list-group-item-text {
	margin-bottom: 0;
	line-height: 1.3
}

.panel {
	margin-bottom: 20px;
	background-color: #fff;
	border: 1px solid transparent;
	border-radius: 4px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}

.panel-body {
	padding: 15px
}

.panel-heading {
	padding: 10px 15px;
	border-bottom: 1px solid transparent;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.panel-heading>.dropdown .dropdown-toggle {
	color: inherit
}

.panel-title {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 16px;
	color: inherit
}

.panel-title>.small,
.panel-title>.small>a,
.panel-title>a,
.panel-title>small,
.panel-title>small>a {
	color: inherit
}

.panel-footer {
	padding: 10px 15px;
	background-color: #f5f5f5;
	border-top: 1px solid #ddd;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}

.panel>.list-group,
.panel>.panel-collapse>.list-group {
	margin-bottom: 0
}

.panel>.list-group .list-group-item,
.panel>.panel-collapse>.list-group .list-group-item {
	border-width: 1px 0;
	border-radius: 0
}

.panel>.list-group:first-child .list-group-item:first-child,
.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child {
	border-top: 0;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.panel>.list-group:last-child .list-group-item:last-child,
.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child {
	border-bottom: 0;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}

.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child {
	border-top-left-radius: 0;
	border-top-right-radius: 0
}

.panel-heading+.list-group .list-group-item:first-child {
	border-top-width: 0
}

.list-group+.panel-footer {
	border-top-width: 0
}

.panel>.panel-collapse>.table,
.panel>.table,
.panel>.table-responsive>.table {
	margin-bottom: 0
}

.panel>.panel-collapse>.table caption,
.panel>.table caption,
.panel>.table-responsive>.table caption {
	padding-right: 15px;
	padding-left: 15px
}

.panel>.table-responsive:first-child>.table:first-child,
.panel>.table:first-child {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:first-child {
	border-top-left-radius: 3px
}

.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,
.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:last-child {
	border-top-right-radius: 3px
}

.panel>.table-responsive:last-child>.table:last-child,
.panel>.table:last-child {
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}

.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child {
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}

.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child,
.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
	border-bottom-left-radius: 3px
}

.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
	border-bottom-right-radius: 3px
}

.panel>.panel-body+.table,
.panel>.panel-body+.table-responsive,
.panel>.table+.panel-body,
.panel>.table-responsive+.panel-body {
	border-top: 1px solid #ddd
}

.panel>.table>tbody:first-child>tr:first-child td,
.panel>.table>tbody:first-child>tr:first-child th {
	border-top: 0
}

.panel>.table-bordered,
.panel>.table-responsive>.table-bordered {
	border: 0
}

.panel>.table-bordered>tbody>tr>td:first-child,
.panel>.table-bordered>tbody>tr>th:first-child,
.panel>.table-bordered>tfoot>tr>td:first-child,
.panel>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-bordered>thead>tr>td:first-child,
.panel>.table-bordered>thead>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:first-child {
	border-left: 0
}

.panel>.table-bordered>tbody>tr>td:last-child,
.panel>.table-bordered>tbody>tr>th:last-child,
.panel>.table-bordered>tfoot>tr>td:last-child,
.panel>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-bordered>thead>tr>td:last-child,
.panel>.table-bordered>thead>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:last-child {
	border-right: 0
}

.panel>.table-bordered>tbody>tr:first-child>td,
.panel>.table-bordered>tbody>tr:first-child>th,
.panel>.table-bordered>thead>tr:first-child>td,
.panel>.table-bordered>thead>tr:first-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>th {
	border-bottom: 0
}

.panel>.table-bordered>tbody>tr:last-child>td,
.panel>.table-bordered>tbody>tr:last-child>th,
.panel>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-bordered>tfoot>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th {
	border-bottom: 0
}

.panel>.table-responsive {
	margin-bottom: 0;
	border: 0
}

.panel-group {
	margin-bottom: 20px
}

.panel-group .panel {
	margin-bottom: 0;
	border-radius: 4px
}

.panel-group .panel+.panel {
	margin-top: 5px
}

.panel-group .panel-heading {
	border-bottom: 0
}

.panel-group .panel-heading+.panel-collapse>.list-group,
.panel-group .panel-heading+.panel-collapse>.panel-body {
	border-top: 1px solid #ddd
}

.panel-group .panel-footer {
	border-top: 0
}

.panel-group .panel-footer+.panel-collapse .panel-body {
	border-bottom: 1px solid #ddd
}

.panel-default {
	border-color: #ddd
}

.panel-default>.panel-heading {
	color: #333;
	background-color: #f5f5f5;
	border-color: #ddd
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #ddd
}

.panel-default>.panel-heading .badge {
	color: #f5f5f5;
	background-color: #333
}

.panel-default>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #ddd
}

.panel-primary {
	border-color: #337ab7
}

.panel-primary>.panel-heading {
	color: #fff;
	background-color: #337ab7;
	border-color: #337ab7
}

.panel-primary>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #337ab7
}

.panel-primary>.panel-heading .badge {
	color: #337ab7;
	background-color: #fff
}

.panel-primary>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #337ab7
}

.panel-success {
	border-color: #d6e9c6
}

.panel-success>.panel-heading {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6
}

.panel-success>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #d6e9c6
}

.panel-success>.panel-heading .badge {
	color: #dff0d8;
	background-color: #3c763d
}

.panel-success>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #d6e9c6
}

.panel-info {
	border-color: #bce8f1
}

.panel-info>.panel-heading {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1
}

.panel-info>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #bce8f1
}

.panel-info>.panel-heading .badge {
	color: #d9edf7;
	background-color: #31708f
}

.panel-info>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #bce8f1
}

.panel-warning {
	border-color: #faebcc
}

.panel-warning>.panel-heading {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc
}

.panel-warning>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #faebcc
}

.panel-warning>.panel-heading .badge {
	color: #fcf8e3;
	background-color: #8a6d3b
}

.panel-warning>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #faebcc
}

.panel-danger {
	border-color: #ebccd1
}

.panel-danger>.panel-heading {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1
}

.panel-danger>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #ebccd1
}

.panel-danger>.panel-heading .badge {
	color: #f2dede;
	background-color: #a94442
}

.panel-danger>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #ebccd1
}

.embed-responsive {
	position: relative;
	display: block;
	height: 0;
	padding: 0;
	overflow: hidden
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 0
}

.embed-responsive-16by9 {
	padding-bottom: 56.25%
}

.embed-responsive-4by3 {
	padding-bottom: 75%
}

.well {
	min-height: 20px;
	padding: 19px;
	margin-bottom: 20px;
	background-color: #f5f5f5;
	border: 1px solid #e3e3e3;
	border-radius: 4px;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05)
}

.well blockquote {
	border-color: #ddd
}

.well-lg {
	padding: 24px;
	border-radius: 6px
}

.well-sm {
	padding: 9px;
	border-radius: 3px
}

.close {
	float: right;
	font-size: 21px;
	font-weight: 700;
	line-height: 1;
	color: #000/*! text-shadow:0 1px 0 #fff; */
	/*! opacity:.2 */
}

.close:focus,
.close:hover {
	color: #000;
	text-decoration: none;
	cursor: pointer/*! opacity:.5 */
}

button.close {
	padding: 0;
	cursor: pointer;
	background: 0 0;
	border: 0
}

.modal-open {
	overflow: hidden
}

.modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1050;
	display: none;
	overflow: hidden;
	outline: 0
}

.modal.fade .modal-dialog {
	transition: transform .3s ease-out;
	transform: translate(0, -25%)
}

.modal.in .modal-dialog {
	transform: translate(0, 0)
}

.modal-open .modal {
	overflow-x: hidden;
	overflow-y: auto
}

.modal-dialog {
	position: relative;
	width: auto;
	margin: 10px
}

.modal-content {
	position: relative;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #999;
	border-radius: 6px;
	outline: 0;
	box-shadow: 0 3px 9px rgba(0, 0, 0, .5)
}

.modal-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1040;
	background-color: #000
}

.modal-backdrop.fade {
	opacity: 0
}

.modal-backdrop.in {
	opacity: .5
}

.modal-header {
	padding: 15px;
	border-bottom: 1px solid #e5e5e5
}

.modal-header .close {
	margin-top: -2px
}

.modal-title {
	margin: 0;
	line-height: 1.42857143
}

.modal-body {
	position: relative;
	padding: 15px
}

.modal-footer {
	padding: 15px;
	text-align: right;
	border-top: 1px solid #e5e5e5
}

.modal-footer .btn+.btn {
	margin-bottom: 0;
	margin-left: 5px
}

.modal-footer .btn-group .btn+.btn {
	margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
	margin-left: 0
}

.modal-scrollbar-measure {
	position: absolute;
	top: -9999px;
	width: 50px;
	height: 50px;
	overflow: scroll
}

@media (min-width:768px) {
	.modal-dialog {
		width: 600px;
		margin: 30px auto
	}
	.modal-content {
		box-shadow: 0 5px 15px rgba(0, 0, 0, .5)
	}
	.modal-sm {
		width: 300px
	}
}

@media (min-width:992px) {
	.modal-lg {
		width: 900px
	}
}

.tooltip {
	position: absolute;
	z-index: 1070;
	display: block;
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: left;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	word-wrap: normal;
	white-space: normal;
	opacity: 0;
	line-break: auto
}

.tooltip.in {
	opacity: .9
}

.tooltip.top {
	padding: 5px 0;
	margin-top: -3px
}

.tooltip.right {
	padding: 0 5px;
	margin-left: 3px
}

.tooltip.bottom {
	padding: 5px 0;
	margin-top: 3px
}

.tooltip.left {
	padding: 0 5px;
	margin-left: -3px
}

.tooltip-inner {
	max-width: 200px;
	padding: 3px 8px;
	color: #fff;
	text-align: center;
	background-color: #000;
	border-radius: 4px
}

.tooltip-arrow {
	position: absolute;
	width: 0;
	height: 0;
	border-color: transparent;
	border-style: solid
}

.tooltip.top .tooltip-arrow {
	bottom: 0;
	left: 50%;
	margin-left: -5px;
	border-width: 5px 5px 0;
	border-top-color: #000
}

.tooltip.top-left .tooltip-arrow {
	right: 5px;
	bottom: 0;
	margin-bottom: -5px;
	border-width: 5px 5px 0;
	border-top-color: #000
}

.tooltip.top-right .tooltip-arrow {
	bottom: 0;
	left: 5px;
	margin-bottom: -5px;
	border-width: 5px 5px 0;
	border-top-color: #000
}

.tooltip.right .tooltip-arrow {
	top: 50%;
	left: 0;
	margin-top: -5px;
	border-width: 5px 5px 5px 0;
	border-right-color: #000
}

.tooltip.left .tooltip-arrow {
	top: 50%;
	right: 0;
	margin-top: -5px;
	border-width: 5px 0 5px 5px;
	border-left-color: #000
}

.tooltip.bottom .tooltip-arrow {
	top: 0;
	left: 50%;
	margin-left: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}

.tooltip.bottom-left .tooltip-arrow {
	top: 0;
	right: 5px;
	margin-top: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}

.tooltip.bottom-right .tooltip-arrow {
	top: 0;
	left: 5px;
	margin-top: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}

.popover {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1060;
	display: none;
	max-width: 276px;
	padding: 1px;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: left;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	word-wrap: normal;
	white-space: normal;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #ccc;
	border-radius: 6px;
	box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
	line-break: auto
}

.popover.top {
	margin-top: -10px
}

.popover.right {
	margin-left: 10px
}

.popover.bottom {
	margin-top: 10px
}

.popover.left {
	margin-left: -10px
}

.popover-title {
	padding: 8px 14px;
	margin: 0;
	font-size: 14px;
	background-color: #f7f7f7;
	border-bottom: 1px solid #ebebeb;
	border-radius: 5px 5px 0 0
}

.popover-content {
	padding: 9px 14px
}

.popover>.arrow,
.popover>.arrow:after {
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	border-color: transparent;
	border-style: solid
}

.popover>.arrow {
	border-width: 11px
}

.popover>.arrow:after {
	content: "";
	border-width: 10px
}

.popover.top>.arrow {
	bottom: -11px;
	left: 50%;
	margin-left: -11px;
	border-top-color: #999;
	border-bottom-width: 0
}

.popover.top>.arrow:after {
	bottom: 1px;
	margin-left: -10px;
	content: " ";
	border-top-color: #fff;
	border-bottom-width: 0
}

.popover.right>.arrow {
	top: 50%;
	left: -11px;
	margin-top: -11px;
	border-right-color: #999;
	border-left-width: 0
}

.popover.right>.arrow:after {
	bottom: -10px;
	left: 1px;
	content: " ";
	border-right-color: #fff;
	border-left-width: 0
}

.popover.bottom>.arrow {
	top: -11px;
	left: 50%;
	margin-left: -11px;
	border-top-width: 0;
	border-bottom-color: #999
}

.popover.bottom>.arrow:after {
	top: 1px;
	margin-left: -10px;
	content: " ";
	border-top-width: 0;
	border-bottom-color: #fff
}

.popover.left>.arrow {
	top: 50%;
	right: -11px;
	margin-top: -11px;
	border-right-width: 0;
	border-left-color: #999
}

.popover.left>.arrow:after {
	right: 1px;
	bottom: -10px;
	content: " ";
	border-right-width: 0;
	border-left-color: #fff
}

.carousel {
	position: relative
}

.carousel-inner {
	position: relative;
	width: 100%;
	overflow: hidden
}

.carousel-inner>.item {
	position: relative;
	display: none;
	transition: .6s ease-in-out left
}

.carousel-inner>.item>a>img,
.carousel-inner>.item>img {
	line-height: 1
}

@media all and (transform-3d),
(-webkit-transform-3d) {
	.carousel-inner>.item {
		transition: transform .6s ease-in-out;
		backface-visibility: hidden;
		perspective: 1000px
	}
	.carousel-inner>.item.active.right,
	.carousel-inner>.item.next {
		left: 0;
		transform: translate3d(100%, 0, 0)
	}
	.carousel-inner>.item.active.left,
	.carousel-inner>.item.prev {
		left: 0;
		transform: translate3d(-100%, 0, 0)
	}
	.carousel-inner>.item.active,
	.carousel-inner>.item.next.left,
	.carousel-inner>.item.prev.right {
		left: 0;
		transform: translate3d(0, 0, 0)
	}
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
	display: block
}

.carousel-inner>.active {
	left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
	position: absolute;
	top: 0;
	width: 100%
}

.carousel-inner>.next {
	left: 100%
}

.carousel-inner>.prev {
	left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
	left: 0
}

.carousel-inner>.active.left {
	left: -100%
}

.carousel-inner>.active.right {
	left: 100%
}

.carousel-control {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 15%;
	font-size: 20px;
	color: #fff;
	text-align: center;
	text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
	background-color: rgba(0, 0, 0, 0);
	opacity: .5
}

.carousel-control.left {
	background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
	background-image: -o-linear-gradient(left, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
	background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, .0001)));
	background-image: linear-gradient(to right, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
	background-repeat: repeat-x
}

.carousel-control.right {
	right: 0;
	left: auto;
	background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
	background-image: -o-linear-gradient(left, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
	background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .0001)), to(rgba(0, 0, 0, .5)));
	background-image: linear-gradient(to right, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
	background-repeat: repeat-x
}

.carousel-control:focus,
.carousel-control:hover {
	color: #fff;
	text-decoration: none;
	outline: 0;
	opacity: .9
}

.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right,
.carousel-control .icon-next,
.carousel-control .icon-prev {
	position: absolute;
	top: 50%;
	z-index: 5;
	display: inline-block;
	margin-top: -10px
}

.carousel-control .glyphicon-chevron-left,
.carousel-control .icon-prev {
	left: 50%;
	margin-left: -10px
}

.carousel-control .glyphicon-chevron-right,
.carousel-control .icon-next {
	right: 50%;
	margin-right: -10px
}

.carousel-control .icon-next,
.carousel-control .icon-prev {
	width: 20px;
	height: 20px;
	font-family: serif;
	line-height: 1
}

.carousel-control .icon-prev:before {
	content: '\2039'
}

.carousel-control .icon-next:before {
	content: '\203a'
}

.carousel-indicators {
	position: absolute;
	bottom: 10px;
	left: 50%;
	z-index: 15;
	width: 60%;
	padding-left: 0;
	margin-left: -30%;
	text-align: center;
	list-style: none
}

.carousel-indicators li {
	display: inline-block;
	width: 10px;
	height: 10px;
	margin: 1px;
	text-indent: -999px;
	cursor: pointer;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #fff;
	border-radius: 10px
}

.carousel-indicators .active {
	width: 12px;
	height: 12px;
	margin: 0;
	background-color: #fff
}

.carousel-caption {
	position: absolute;
	right: 15%;
	bottom: 20px;
	left: 15%;
	z-index: 10;
	padding-top: 20px;
	padding-bottom: 20px;
	color: #fff;
	text-align: center;
	text-shadow: 0 1px 2px rgba(0, 0, 0, .6)
}

.carousel-caption .btn {
	text-shadow: none
}

@media screen and (min-width:768px) {
	.carousel-control .glyphicon-chevron-left,
	.carousel-control .glyphicon-chevron-right,
	.carousel-control .icon-next,
	.carousel-control .icon-prev {
		width: 30px;
		height: 30px;
		margin-top: -10px;
		font-size: 30px
	}
	.carousel-control .glyphicon-chevron-left,
	.carousel-control .icon-prev {
		margin-left: -10px
	}
	.carousel-control .glyphicon-chevron-right,
	.carousel-control .icon-next {
		margin-right: -10px
	}
	.carousel-caption {
		right: 20%;
		left: 20%;
		padding-bottom: 30px
	}
	.carousel-indicators {
		bottom: 20px
	}
}

.slideOutUp {
	animation-name: slideOutUp
}

.owl-carousel,
.owl-carousel .owl-item {
	position: relative;
	-webkit-tap-highlight-color: transparent;
	position: relative;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0)
}

.owl-carousel {
	display: none;
	width: 100%;
	z-index: 1
}

.owl-carousel .owl-stage {
	position: relative;
	position: relative;
	-ms-touch-action: pan-Y;
	touch-action: manipulation;
	-moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0
}

.owl-carousel .owl-stage-outer {
	position: relative;
	overflow: hidden;
	position: relative;
	overflow: hidden;
	-webkit-transform: translate3d(0, 0, 0);
	margin-bottom: 10px
}

.owl-carousel .owl-item {
	min-height: 1px;
	float: left;
	-webkit-backface-visibility: hidden;
	-webkit-touch-callout: none
}

.owl-carousel.owl-drag .owl-item {
	-ms-touch-action: pan-y;
	touch-action: pan-y;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.owl-carousel .owl-item img {
	display: block;
	width: 100%
}

.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
	display: none
}

.no-js .owl-carousel,
.owl-carousel.owl-loaded {
	display: block
}

.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
	cursor: pointer;
	display: none
}

.owl-carousel.owl-loading {
	opacity: 0;
	display: block
}

.owl-carousel.owl-hidden {
	opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
	visibility: hidden
}

.owl-carousel.owl-grab {
	cursor: move;
	cursor: grab
}

.owl-carousel.owl-rtl {
	direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
	float: right
}

.owl-carousel .animated {
	animation-duration: 1s
}

.owl-carousel .owl-animated-in {
	z-index: 0
}

.owl-carousel .owl-animated-out {
	z-index: 1
}

.owl-carousel .fadeOut {
	animation-name: fadeOut
}

@-webkit-keyframes fadeOut {
	0% {
		opacity: 1
	}
	100% {
		opacity: 0
	}
}

@keyframes fadeOut {
	0% {
		opacity: 1
	}
	100% {
		opacity: 0
	}
}

.owl-height {
	transition: height .5s ease-in-out
}

.owl-carousel .owl-item .owl-lazy {
	opacity: 0;
	transition: opacity .4s ease
}

.owl-carousel .owl-item img.owl-lazy {
	transform-style: preserve-3d
}

.owl-carousel .owl-video-wrapper {
	position: relative;
	height: 100%;
	background: #000
}

.owl-carousel .owl-video-play-icon {
	position: absolute;
	height: 80px;
	width: 80px;
	left: 50%;
	top: 50%;
	margin-left: -40px;
	margin-top: -40px;
	background: url(owl.video.play.png) no-repeat;
	cursor: pointer;
	z-index: 1;
	transition: -webkit-transform .1s ease
}

.owl-carousel .owl-video-play-icon:hover {
	transform: scale(1.3, 1.3)
}

.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
	display: none
}

.owl-carousel .owl-video-tn {
	opacity: 0;
	height: 100%;
	background-position: center center;
	background-repeat: no-repeat;
	background-size: contain;
	transition: opacity .4s ease
}

.owl-carousel .owl-video-frame {
	position: relative;
	z-index: 1;
	height: 100%;
	width: 100%
}

.owl-theme .owl-nav {
	margin-top: 10px;
	text-align: center
}

.owl-theme .owl-nav[class*=owl-] {
	color: #fff;
	font-size: 14px;
	margin: 5px;
	padding: 4px 7px;
	background: #d6d6d6;
	display: inline-block;
	cursor: pointer;
	border-radius: 3px
}

.owl-theme .owl-nav[class*=owl-]:hover {
	background: #869791;
	color: #fff;
	text-decoration: none
}

.owl-theme .owl-nav .disabled {
	opacity: .5;
	cursor: default
}

.owl-theme .owl-nav.disabled+.owl-dots {
	margin-top: 10px
}

.owl-theme .owl-dots {
	text-align: center
}

.owl-theme .owl-dots .owl-dot {
	display: inline-block
}

.owl-theme .owl-dots .owl-dot span {
	width: 10px;
	height: 10px;
	margin: 5px 7px;
	background: #d6d6d6;
	display: block;
	transition: opacity .2s ease;
	border-radius: 30px
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
	background: #869791
}

.box {
	position: relative;
	border-radius: 3px;
	background: #fff;
	border-top: 3px solid #d2d6de;
	margin-bottom: 20px;
	width: 100%;
	box-shadow: 0 1px 1px rgba(0, 0, 0, .1)
}

.box.box-primary {
	border-top-color: #3c8dbc
}

.box.box-info {
	border-top-color: #00c0ef
}

.box.box-danger {
	border-top-color: #dd4b39
}

.box.box-warning {
	border-top-color: #f39c12
}

.box.box-success {
	border-top-color: #00a65a
}

.box.box-default {
	border-top-color: #d2d6de
}

.box.collapsed-box .box-body,
.box.collapsed-box .box-footer {
	display: none
}

.box .nav-stacked>li {
	border-bottom: 1px solid #f4f4f4;
	margin: 0
}

.box .nav-stacked>li:last-of-type {
	border-bottom: none
}

.box.height-control .box-body {
	max-height: 300px;
	overflow: auto
}

.box .border-right {
	border-right: 1px solid #f4f4f4
}

.box .border-left {
	border-left: 1px solid #f4f4f4
}

.box.box-solid {
	border-top: 0
}

.box.box-solid>.box-header .btn.btn-default {
	background: 0 0
}

.box.box-solid>.box-header .btn:hover,
.box.box-solid>.box-header a:hover {
	background: rgba(0, 0, 0, .1)
}

.box.box-solid.box-default {
	border: 1px solid #d2d6de
}

.box.box-solid.box-default>.box-header {
	color: #444;
	background: #d2d6de;
	background-color: #d2d6de
}

.box.box-solid.box-default>.box-header .btn,
.box.box-solid.box-default>.box-header a {
	color: #444
}

.box.box-solid.box-primary {
	border: 1px solid #3c8dbc
}

.box.box-solid.box-primary>.box-header {
	color: #fff;
	background: #3c8dbc;
	background-color: #3c8dbc
}

.box.box-solid.box-primary>.box-header .btn,
.box.box-solid.box-primary>.box-header a {
	color: #fff
}

.box.box-solid.box-info {
	border: 1px solid #00c0ef
}

.box.box-solid.box-info>.box-header {
	color: #fff;
	background: #00c0ef;
	background-color: #00c0ef
}

.box.box-solid.box-info>.box-header .btn,
.box.box-solid.box-info>.box-header a {
	color: #fff
}

.box.box-solid.box-danger {
	border: 1px solid #dd4b39
}

.box.box-solid.box-danger>.box-header {
	color: #fff;
	background: #dd4b39;
	background-color: #dd4b39
}

.box.box-solid.box-danger>.box-header .btn,
.box.box-solid.box-danger>.box-header a {
	color: #fff
}

.box.box-solid.box-warning {
	border: 1px solid #f39c12
}

.box.box-solid.box-warning>.box-header {
	color: #fff;
	background: #f39c12;
	background-color: #f39c12
}

.box.box-solid.box-warning>.box-header .btn,
.box.box-solid.box-warning>.box-header a {
	color: #fff
}

.box.box-solid.box-success {
	border: 1px solid #00a65a
}

.box.box-solid.box-success>.box-header {
	color: #fff;
	background: #00a65a;
	background-color: #00a65a
}

.box.box-solid.box-success>.box-header .btn,
.box.box-solid.box-success>.box-header a {
	color: #fff
}

.box.box-solid>.box-header>.box-tools .btn {
	border: 0;
	box-shadow: none
}

.box.box-solid[class*=bg]>.box-header {
	color: #fff
}

.box .box-group>.box {
	margin-bottom: 5px
}

.box .knob-label {
	text-align: center;
	color: #333;
	font-weight: 100;
	font-size: 12px;
	margin-bottom: .3em
}

.box>.loading-img,
.box>.overlay,
.overlay-wrapper>.loading-img,
.overlay-wrapper>.overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%
}

.box .overlay,
.overlay-wrapper .overlay {
	z-index: 50;
	background: rgba(255, 255, 255, .7);
	border-radius: 3px
}

.box .overlay>.fa,
.overlay-wrapper .overlay>.fa {
	position: absolute;
	top: 50%;
	left: 50%;
	margin-left: -15px;
	margin-top: -15px;
	color: #000;
	font-size: 30px
}

.box .overlay.dark,
.overlay-wrapper .overlay.dark {
	background: rgba(0, 0, 0, .5)
}

.box-body:after,
.box-body:before,
.box-footer:after,
.box-footer:before,
.box-header:after,
.box-header:before {
	content: " ";
	display: table
}

.box-body:after,
.box-footer:after,
.box-header:after {
	clear: both
}

.box-header {
	color: #444;
	display: block;
	padding: 10px;
	position: relative
}

.box-header.with-border {
	border-bottom: 1px solid #f4f4f4
}

.collapsed-box .box-header.with-border {
	border-bottom: none
}

.box-header .box-title,
.box-header>.fa,
.box-header>.glyphicon,
.box-header>.ion {
	display: inline-block;
	font-size: 18px;
	margin: 0;
	line-height: 1
}

.box-header>.fa,
.box-header>.glyphicon,
.box-header>.ion {
	margin-right: 5px
}

.box-header>.box-tools {
	position: absolute;
	right: 10px;
	top: 5px
}

.box-header>.box-tools[data-toggle=tooltip] {
	position: relative
}

.box-header>.box-tools.pull-right .dropdown-menu {
	right: 0;
	left: auto
}

.box-header>.box-tools .dropdown-menu>li>a {
	color: #444!important
}

.btn-box-tool {
	padding: 5px;
	font-size: 12px;
	background: 0 0;
	color: #97a0b3
}

.btn-box-tool:hover,
.open .btn-box-tool {
	color: #606c84
}

.btn-box-tool.btn:active {
	box-shadow: none
}

.box-body {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;
	padding: 10px
}

.no-header .box-body {
	border-top-right-radius: 3px;
	border-top-left-radius: 3px
}

.box-body>.table {
	margin-bottom: 0
}

.box-body .fc {
	margin-top: 5px
}

.box-body .full-width-chart {
	margin: -19px
}

.box-body.no-padding .full-width-chart {
	margin: -9px
}

.box-body .box-pane {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 3px
}

.box-body .box-pane-right {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 0
}

.box-footer {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;
	border-top: 1px solid #f4f4f4;
	padding: 10px;
	background-color: #fff
}

body.frame {
	overflow: hidden;
	padding-top: 0
}

body.frame .banner {
	margin: 0
}

@media (max-width:768px) {
	body.frame .counter {
		padding-top: 8px
	}
}

.advertising-rates table th[colspan='3'],
.advertising-rates table th[rowspan='2'],
.payout-rates table th[colspan='2'],
.payout-rates table th[rowspan='2'] {
	vertical-align: middle;
	text-align: center
}

.display-counter {
	font-size: 70px;
	display: block;
	color: #336799
}

.countdown {
	border: 2px solid #56f7a6;
	border-radius: 50%;
	color: #9fc;
	display: block;
	font-size: 15px;
	font-weight: 300;
	height: 100px;
	line-height: 18px;
	margin: 25px auto;
	padding: 29px 0 0;
	width: 100px
}

.countdown .timer {
	font-size: 25px;
	text-align: center!important;
	color: #76ffc7
}

.banner {
	text-align: center;
	margin-bottom: 10px
}

.banner .banner-inner {
	margin: 0 auto
}

.banner-captcha .banner-inner {
	max-width: 728px
}

.banner-member .banner-inner {
	max-width: 728px
}

.banner-728x90 .banner-inner {
	max-width: 728px
}

.banner-468x60 .banner-inner {
	max-width: 468px
}

.banner-336x280 .banner-inner {
	max-width: 336px
}

.cookie-message {
	color: #fff
}

.cookie-message a {
	color: #fff
}

.cookie-confirm {
	text-align: right;
	margin-left: -10px
}

@media (min-width:768px) {
	.cookie-message {
		padding: 20px 0
	}
	.cookie-confirm {
		padding-top: 8px
	}
}

@media (max-width:767px) {
	#cookie-pop {
		padding-top: 15px;
		padding-bottom: 15px
	}
	.main-logo {
		display: none!important
	}
}

.btn-group,
.btn-group-vertical {
	position: relative;
	display: inline-block;
	vertical-align: middle
}

.btn-group-vertical>.btn,
.btn-group>.btn {
	position: relative;
	float: left
}

.btn-group-vertical>.btn.active,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:hover,
.btn-group>.btn.active,
.btn-group>.btn:active,
.btn-group>.btn:focus,
.btn-group>.btn:hover {
	z-index: 2
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group {
	margin-left: -1px
}

.btn-toolbar {
	margin-left: -5px
}

.btn-toolbar .btn,
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
	float: left
}

.btn-toolbar>.btn,
.btn-toolbar>.btn-group,
.btn-toolbar>.input-group {
	margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
	border-radius: 0
}

.btn-group>.btn:first-child {
	margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}

.btn-group>.btn-group {
	float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
	border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
	outline: 0
}

.btn-group>.btn+.dropdown-toggle {
	padding-right: 8px;
	padding-left: 8px
}

.btn-group>.btn-lg+.dropdown-toggle {
	padding-right: 12px;
	padding-left: 12px
}

.btn-group.open .dropdown-toggle {
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.btn-group.open .dropdown-toggle.btn-link {
	box-shadow: none
}

.btn .caret {
	margin-left: 0
}

.btn-lg .caret {
	border-width: 5px 5px 0
}

.dropup .btn-lg .caret {
	border-width: 0 5px 5px
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group,
.btn-group-vertical>.btn-group>.btn {
	display: block;
	float: none;
	width: 100%;
	max-width: 100%
}

.btn-group-vertical>.btn-group>.btn {
	float: none
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
	margin-top: -1px;
	margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
	border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
	border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
	border-top-left-radius: 0;
	border-top-right-radius: 0
}

.btn-group-justified {
	display: table;
	width: 100%;
	table-layout: fixed;
	border-collapse: separate
}

.btn-group-justified>.btn,
.btn-group-justified>.btn-group {
	display: table-cell;
	float: none;
	width: 1%
}

.btn-group-justified>.btn-group .btn {
	width: 100%
}

.btn-group-justified>.btn-group .dropdown-menu {
	left: auto
}

[data-toggle=buttons]>.btn input[type=checkbox],
[data-toggle=buttons]>.btn input[type=radio],
[data-toggle=buttons]>.btn-group>.btn input[type=checkbox],
[data-toggle=buttons]>.btn-group>.btn input[type=radio] {
	position: absolute;
	clip: rect(0, 0, 0, 0)
}

.input-group {
	position: relative;
	display: table;
	border-collapse: separate;
	box-shadow: 0 0 50px 5px rgba(214, 214, 214, .29)
}

.input-group[class*=col-] {
	float: none;
	padding-right: 0;
	padding-left: 0
}

.input-group .form-control {
	position: relative;
	z-index: 2;
	float: left;
	width: 100%;
	margin-bottom: 0
}

.input-group .form-control:focus {
	z-index: 3
}

.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}

select.input-group-lg>.form-control,
select.input-group-lg>.input-group-addon,
select.input-group-lg>.input-group-btn>.btn {
	height: 46px;
	line-height: 46px
}

select[multiple].input-group-lg>.form-control,
select[multiple].input-group-lg>.input-group-addon,
select[multiple].input-group-lg>.input-group-btn>.btn,
textarea.input-group-lg>.form-control,
textarea.input-group-lg>.input-group-addon,
textarea.input-group-lg>.input-group-btn>.btn {
	height: auto
}

.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

select.input-group-sm>.form-control,
select.input-group-sm>.input-group-addon,
select.input-group-sm>.input-group-btn>.btn {
	height: 30px;
	line-height: 30px
}

select[multiple].input-group-sm>.form-control,
select[multiple].input-group-sm>.input-group-addon,
select[multiple].input-group-sm>.input-group-btn>.btn,
textarea.input-group-sm>.form-control,
textarea.input-group-sm>.input-group-addon,
textarea.input-group-sm>.input-group-btn>.btn {
	height: auto
}

.input-group .form-control,
.input-group-addon,
.input-group-btn {
	display: table-cell
}

.input-group .form-control:not(:first-child):not(:last-child),
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child) {
	border-radius: 0
}

.input-group-addon,
.input-group-btn {
	width: 1%;
	white-space: nowrap;
	vertical-align: middle
}

.input-group-addon {
	padding: 6px 12px;
	font-size: 14px;
	font-weight: 400;
	line-height: 1;
	color: #43484f;
	text-align: center;
	background-color: #fff;
	border: 1px solid #43484f;
	border-radius: 4px;
	border-bottom: 0;
	border-top: 0
}

.input-group-addon.input-sm {
	padding: 5px 10px;
	font-size: 12px;
	border-radius: 3px
}

.input-group-addon.input-lg {
	padding: 10px 16px;
	font-size: 18px;
	border-radius: 6px
}

.input-group-addon input[type=checkbox],
.input-group-addon input[type=radio] {
	margin-top: 0
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group>.btn,
.input-group-btn:first-child>.dropdown-toggle,
.input-group-btn:last-child>.btn-group:not(:last-child)>.btn,
.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle) {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}

.input-group-addon:first-child {
	border-right: 0
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:first-child>.btn-group:not(:first-child)>.btn,
.input-group-btn:first-child>.btn:not(:first-child),
.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group>.btn,
.input-group-btn:last-child>.dropdown-toggle {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}

.input-group-addon:last-child {
	border-left: 0
}

.input-group-btn {
	position: relative;
	font-size: 0;
	white-space: nowrap
}

.input-group-btn>.btn {
	position: relative
}

.input-group-btn>.btn+.btn {
	margin-left: -1px
}

.input-group-btn>.btn:active,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:hover {
	z-index: 2
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
	margin-right: -1px
}

.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group {
	z-index: 2;
	margin-left: -1px
}

.btn-contact {
	color: #fff;
	background: #00ea8a
}

.btn {
	display: inline-block;
	padding: 12px 20px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	cursor: pointer;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 27px
}

.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
	outline: 0;
	opacity: .8
}

.btn.focus,
.btn:focus,
.btn:hover {
	text-decoration: none
}

.btn.active,
.btn:active {
	background-image: none;
	outline: 0;
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
	cursor: not-allowed;
	box-shadow: none;
	opacity: .2
}

.btn-default {
	color: #fff;
	background-color: #39385b
}

.btn-default.focus,
.btn-default:focus {
	color: #333;
	background-color: #e6e6e6;
	border-color: #8c8c8c
}

.btn-default:hover {
	color: #141823;
	background-color: #e6e6e6
}

.btn-default.active,
.btn-default:active,
.open>.dropdown-toggle.btn-default {
	color: #333;
	background-color: #e6e6e6;
	border-color: #adadad
}

.btn-default.active.focus,
.btn-default.active:focus,
.btn-default.active:hover,
.btn-default:active.focus,
.btn-default:active:focus,
.btn-default:active:hover,
.open>.dropdown-toggle.btn-default.focus,
.open>.dropdown-toggle.btn-default:focus,
.open>.dropdown-toggle.btn-default:hover {
	color: #333;
	background-color: #d4d4d4;
	border-color: #8c8c8c
}

.btn-default.active,
.btn-default:active,
.open>.dropdown-toggle.btn-default {
	background-image: none
}

.btn-default.disabled.focus,
.btn-default.disabled:focus,
.btn-default.disabled:hover,
.btn-default[disabled].focus,
.btn-default[disabled]:focus,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default.focus,
fieldset[disabled] .btn-default:focus,
fieldset[disabled] .btn-default:hover {
	background-color: #fff;
	border-color: #ccc
}

.btn-default .badge {
	color: #fff;
	background-color: #333
}

.btn-primary {
	border: 2px solid #00ea8a;
	color: #fff;
	background: #00ea8a;
	font-weight: 500;
	padding: 12px 25px;
	font-size: 17px
}

.btn-primary:hover {
	color: #fff;
	border-color: #69ddad;
	background: #69ddad
}

.btn-primary.active,
.btn-primary:active,
.open>.dropdown-toggle.btn-primary {
	color: #00ea8a;
	background-image: none
}

.btn-primary .badge {
	color: #337ab7;
	background-color: #fff
}

.btn-primary2 {
	color: #fff;
	background-color: #00ea8a
}

.btn-primary2:hover {
	color: #fff
}

.btn-success2:hover {
	color: #fff
}

.btn-success2 {
	color: #fff;
	background-color: #5cb85c
}

.btn-success {
	color: #fff;
	background-color: #5cb85c
}

.btn-success.focus,
.btn-success:focus {
	color: #fff
}

.btn-success:hover {
	color: #fff;
	border-color: #69ddad;
	background: #69ddad
}

.btn-success.active,
.btn-success:active,
.open>.dropdown-toggle.btn-success {
	color: #00ea8a;
	background-color: #fff;
	border-color: #00ea8a;
	background-image: none
}

.btn-success .badge {
	color: #5cb85c;
	background-color: #fff
}

.btn-info {
	color: #fff;
	background-color: #5bc0de;
	border-color: #46b8da
}

.btn-info.focus,
.btn-info:focus {
	color: #fff;
	background-color: #31b0d5;
	border-color: #1b6d85
}

.btn-info:hover {
	color: #fff;
	background-color: #31b0d5;
	border-color: #269abc
}

.btn-info.active,
.btn-info:active,
.open>.dropdown-toggle.btn-info {
	color: #fff;
	background-color: #31b0d5;
	border-color: #269abc
}

.btn-info.active.focus,
.btn-info.active:focus,
.btn-info.active:hover,
.btn-info:active.focus,
.btn-info:active:focus,
.btn-info:active:hover,
.open>.dropdown-toggle.btn-info.focus,
.open>.dropdown-toggle.btn-info:focus,
.open>.dropdown-toggle.btn-info:hover {
	color: #fff;
	background-color: #269abc;
	border-color: #1b6d85
}

.btn-info.active,
.btn-info:active,
.open>.dropdown-toggle.btn-info {
	background-image: none
}

.btn-info.disabled.focus,
.btn-info.disabled:focus,
.btn-info.disabled:hover,
.btn-info[disabled].focus,
.btn-info[disabled]:focus,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info.focus,
fieldset[disabled] .btn-info:focus,
fieldset[disabled] .btn-info:hover {
	background-color: #5bc0de;
	border-color: #46b8da
}

.btn-info .badge {
	color: #5bc0de;
	background-color: #fff
}

.btn-warning {
	color: #fff;
	background-color: #f0ad4e;
	border-color: #eea236
}

.btn-warning.focus,
.btn-warning:focus {
	color: #fff;
	background-color: #ec971f;
	border-color: #985f0d
}

.btn-warning:hover {
	color: #fff;
	background-color: #ec971f;
	border-color: #d58512
}

.btn-warning.active,
.btn-warning:active,
.open>.dropdown-toggle.btn-warning {
	color: #fff;
	background-color: #ec971f;
	border-color: #d58512
}

.btn-warning.active.focus,
.btn-warning.active:focus,
.btn-warning.active:hover,
.btn-warning:active.focus,
.btn-warning:active:focus,
.btn-warning:active:hover,
.open>.dropdown-toggle.btn-warning.focus,
.open>.dropdown-toggle.btn-warning:focus,
.open>.dropdown-toggle.btn-warning:hover {
	color: #fff;
	background-color: #d58512;
	border-color: #985f0d
}

.btn-warning.active,
.btn-warning:active,
.open>.dropdown-toggle.btn-warning {
	background-image: none
}

.btn-warning.disabled.focus,
.btn-warning.disabled:focus,
.btn-warning.disabled:hover,
.btn-warning[disabled].focus,
.btn-warning[disabled]:focus,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning.focus,
fieldset[disabled] .btn-warning:focus,
fieldset[disabled] .btn-warning:hover {
	background-color: #f0ad4e;
	border-color: #eea236
}

.btn-warning .badge {
	color: #f0ad4e;
	background-color: #fff
}

.btn-danger {
	color: #fff;
	background-color: #d9534f;
	border-color: #d43f3a
}

.btn-danger.focus,
.btn-danger:focus {
	color: #fff;
	background-color: #c9302c;
	border-color: #761c19
}

.btn-danger:hover {
	color: #fff;
	background-color: #c9302c;
	border-color: #ac2925
}

.btn-danger.active,
.btn-danger:active,
.open>.dropdown-toggle.btn-danger {
	color: #fff;
	background-color: #c9302c;
	border-color: #ac2925
}

.btn-danger.active.focus,
.btn-danger.active:focus,
.btn-danger.active:hover,
.btn-danger:active.focus,
.btn-danger:active:focus,
.btn-danger:active:hover,
.open>.dropdown-toggle.btn-danger.focus,
.open>.dropdown-toggle.btn-danger:focus,
.open>.dropdown-toggle.btn-danger:hover {
	color: #fff;
	background-color: #ac2925;
	border-color: #761c19
}

.btn-danger.active,
.btn-danger:active,
.open>.dropdown-toggle.btn-danger {
	background-image: none
}

.btn-danger.disabled.focus,
.btn-danger.disabled:focus,
.btn-danger.disabled:hover,
.btn-danger[disabled].focus,
.btn-danger[disabled]:focus,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger.focus,
fieldset[disabled] .btn-danger:focus,
fieldset[disabled] .btn-danger:hover {
	background-color: #d9534f;
	border-color: #d43f3a
}

.btn-danger .badge {
	color: #d9534f;
	background-color: #fff
}

.btn-link {
	font-weight: 400;
	color: #337ab7;
	border-radius: 0
}

.btn-link,
.btn-link.active,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
	background-color: transparent;
	box-shadow: none
}

.btn-link,
.btn-link:active,
.btn-link:focus,
.btn-link:hover {
	border-color: transparent
}

.btn-link:focus,
.btn-link:hover {
	color: #23527c;
	text-decoration: underline;
	background-color: transparent
}

.btn-link[disabled]:focus,
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:focus,
fieldset[disabled] .btn-link:hover {
	color: #777;
	text-decoration: none
}

.btn-group-lg>.btn,
.btn-lg {
	padding: 15px 20px;
	font-size: 20px;
	line-height: 1.3333333;
	border-radius: 30px;
	border: 2px solid #00ea8a;
	color: #fff;
	background: #00ea8a;
	font-weight: 500
}

.btn-group-sm>.btn,
.btn-sm {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

.btn-group-xs>.btn,
.btn-xs {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

.btn-block {
	display: block;
	width: 100%
}

.btn-block+.btn-block {
	margin-top: 5px
}

.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
	position: static;
	width: auto;
	height: auto;
	margin: 0;
	overflow: visible;
	clip: auto
}

.animated {
	animation-duration: 1s
}

.animated.infinite {
	animation-iteration-count: infinite
}

.animated.hinge {
	animation-duration: 2s
}

.animated.bounceIn,
.animated.bounceOut,
.animated.flipOutX,
.animated.flipOutY {
	animation-duration: .75s
}

@-webkit-keyframes bounce {
	0%,
	20%,
	53%,
	80%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1);
		transform: translateZ(0)
	}
	40%,
	43% {
		transform: translate3d(0, -30px, 0)
	}
	40%,
	43%,
	70% {
		animation-timing-function: cubic-bezier(.755, .05, .855, .06)
	}
	70% {
		transform: translate3d(0, -15px, 0)
	}
	90% {
		transform: translate3d(0, -4px, 0)
	}
}

@keyframes bounce {
	0%,
	20%,
	53%,
	80%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1);
		transform: translateZ(0)
	}
	40%,
	43% {
		transform: translate3d(0, -30px, 0)
	}
	40%,
	43%,
	70% {
		animation-timing-function: cubic-bezier(.755, .05, .855, .06)
	}
	70% {
		transform: translate3d(0, -15px, 0)
	}
	90% {
		transform: translate3d(0, -4px, 0)
	}
}

.bounce {
	animation-name: bounce;
	transform-origin: center bottom
}

@-webkit-keyframes flash {
	0%,
	50%,
	to {
		opacity: 1
	}
	25%,
	75% {
		opacity: 0
	}
}

@keyframes flash {
	0%,
	50%,
	to {
		opacity: 1
	}
	25%,
	75% {
		opacity: 0
	}
}

.flash {
	animation-name: flash
}

@-webkit-keyframes pulse {
	0% {
		transform: scaleX(1)
	}
	50% {
		transform: scale3d(1.05, 1.05, 1.05)
	}
	to {
		transform: scaleX(1)
	}
}

@keyframes pulse {
	0% {
		transform: scaleX(1)
	}
	50% {
		transform: scale3d(1.05, 1.05, 1.05)
	}
	to {
		transform: scaleX(1)
	}
}

.pulse {
	animation-name: pulse
}

@-webkit-keyframes rubberBand {
	0% {
		transform: scaleX(1)
	}
	30% {
		transform: scale3d(1.25, .75, 1)
	}
	40% {
		transform: scale3d(.75, 1.25, 1)
	}
	50% {
		transform: scale3d(1.15, .85, 1)
	}
	65% {
		transform: scale3d(.95, 1.05, 1)
	}
	75% {
		transform: scale3d(1.05, .95, 1)
	}
	to {
		transform: scaleX(1)
	}
}

@keyframes rubberBand {
	0% {
		transform: scaleX(1)
	}
	30% {
		transform: scale3d(1.25, .75, 1)
	}
	40% {
		transform: scale3d(.75, 1.25, 1)
	}
	50% {
		transform: scale3d(1.15, .85, 1)
	}
	65% {
		transform: scale3d(.95, 1.05, 1)
	}
	75% {
		transform: scale3d(1.05, .95, 1)
	}
	to {
		transform: scaleX(1)
	}
}

.rubberBand {
	animation-name: rubberBand
}

@-webkit-keyframes shake {
	0%,
	to {
		transform: translateZ(0)
	}
	10%,
	30%,
	50%,
	70%,
	90% {
		transform: translate3d(-10px, 0, 0)
	}
	20%,
	40%,
	60%,
	80% {
		transform: translate3d(10px, 0, 0)
	}
}

@keyframes shake {
	0%,
	to {
		transform: translateZ(0)
	}
	10%,
	30%,
	50%,
	70%,
	90% {
		transform: translate3d(-10px, 0, 0)
	}
	20%,
	40%,
	60%,
	80% {
		transform: translate3d(10px, 0, 0)
	}
}

.shake {
	animation-name: shake
}

@-webkit-keyframes headShake {
	0% {
		transform: translateX(0)
	}
	6.5% {
		transform: translateX(-6px) rotateY(-9deg)
	}
	18.5% {
		transform: translateX(5px) rotateY(7deg)
	}
	31.5% {
		transform: translateX(-3px) rotateY(-5deg)
	}
	43.5% {
		transform: translateX(2px) rotateY(3deg)
	}
	50% {
		transform: translateX(0)
	}
}

@keyframes headShake {
	0% {
		transform: translateX(0)
	}
	6.5% {
		transform: translateX(-6px) rotateY(-9deg)
	}
	18.5% {
		transform: translateX(5px) rotateY(7deg)
	}
	31.5% {
		transform: translateX(-3px) rotateY(-5deg)
	}
	43.5% {
		transform: translateX(2px) rotateY(3deg)
	}
	50% {
		transform: translateX(0)
	}
}

.headShake {
	animation-timing-function: ease-in-out;
	animation-name: headShake
}

@-webkit-keyframes swing {
	20% {
		transform: rotate(15deg)
	}
	40% {
		transform: rotate(-10deg)
	}
	60% {
		transform: rotate(5deg)
	}
	80% {
		transform: rotate(-5deg)
	}
	to {
		transform: rotate(0)
	}
}

@keyframes swing {
	20% {
		transform: rotate(15deg)
	}
	40% {
		transform: rotate(-10deg)
	}
	60% {
		transform: rotate(5deg)
	}
	80% {
		transform: rotate(-5deg)
	}
	to {
		transform: rotate(0)
	}
}

.swing {
	transform-origin: top center;
	animation-name: swing
}

@-webkit-keyframes tada {
	0% {
		transform: scaleX(1)
	}
	10%,
	20% {
		transform: scale3d(.9, .9, .9) rotate(-3deg)
	}
	30%,
	50%,
	70%,
	90% {
		transform: scale3d(1.1, 1.1, 1.1) rotate(3deg)
	}
	40%,
	60%,
	80% {
		transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg)
	}
	to {
		transform: scaleX(1)
	}
}

@keyframes tada {
	0% {
		transform: scaleX(1)
	}
	10%,
	20% {
		transform: scale3d(.9, .9, .9) rotate(-3deg)
	}
	30%,
	50%,
	70%,
	90% {
		transform: scale3d(1.1, 1.1, 1.1) rotate(3deg)
	}
	40%,
	60%,
	80% {
		transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg)
	}
	to {
		transform: scaleX(1)
	}
}

.tada {
	animation-name: tada
}

@-webkit-keyframes wobble {
	0% {
		transform: none
	}
	15% {
		transform: translate3d(-25%, 0, 0) rotate(-5deg)
	}
	30% {
		transform: translate3d(20%, 0, 0) rotate(3deg)
	}
	45% {
		transform: translate3d(-15%, 0, 0) rotate(-3deg)
	}
	60% {
		transform: translate3d(10%, 0, 0) rotate(2deg)
	}
	75% {
		transform: translate3d(-5%, 0, 0) rotate(-1deg)
	}
	to {
		transform: none
	}
}

@keyframes wobble {
	0% {
		transform: none
	}
	15% {
		transform: translate3d(-25%, 0, 0) rotate(-5deg)
	}
	30% {
		transform: translate3d(20%, 0, 0) rotate(3deg)
	}
	45% {
		transform: translate3d(-15%, 0, 0) rotate(-3deg)
	}
	60% {
		transform: translate3d(10%, 0, 0) rotate(2deg)
	}
	75% {
		transform: translate3d(-5%, 0, 0) rotate(-1deg)
	}
	to {
		transform: none
	}
}

.wobble {
	animation-name: wobble
}

@-webkit-keyframes jello {
	0%,
	11.1%,
	to {
		transform: none
	}
	22.2% {
		transform: skewX(-12.5deg) skewY(-12.5deg)
	}
	33.3% {
		transform: skewX(6.25deg) skewY(6.25deg)
	}
	44.4% {
		transform: skewX(-3.125deg) skewY(-3.125deg)
	}
	55.5% {
		transform: skewX(1.5625deg) skewY(1.5625deg)
	}
	66.6% {
		transform: skewX(-.78125deg) skewY(-.78125deg)
	}
	77.7% {
		transform: skewX(.390625deg) skewY(.390625deg)
	}
	88.8% {
		transform: skewX(-.1953125deg) skewY(-.1953125deg)
	}
}

@keyframes jello {
	0%,
	11.1%,
	to {
		transform: none
	}
	22.2% {
		transform: skewX(-12.5deg) skewY(-12.5deg)
	}
	33.3% {
		transform: skewX(6.25deg) skewY(6.25deg)
	}
	44.4% {
		transform: skewX(-3.125deg) skewY(-3.125deg)
	}
	55.5% {
		transform: skewX(1.5625deg) skewY(1.5625deg)
	}
	66.6% {
		transform: skewX(-.78125deg) skewY(-.78125deg)
	}
	77.7% {
		transform: skewX(.390625deg) skewY(.390625deg)
	}
	88.8% {
		transform: skewX(-.1953125deg) skewY(-.1953125deg)
	}
}

.jello {
	animation-name: jello;
	transform-origin: center
}

@-webkit-keyframes bounceIn {
	0%,
	20%,
	40%,
	60%,
	80%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
	20% {
		transform: scale3d(1.1, 1.1, 1.1)
	}
	40% {
		transform: scale3d(.9, .9, .9)
	}
	60% {
		opacity: 1;
		transform: scale3d(1.03, 1.03, 1.03)
	}
	80% {
		transform: scale3d(.97, .97, .97)
	}
	to {
		opacity: 1;
		transform: scaleX(1)
	}
}

@keyframes bounceIn {
	0%,
	20%,
	40%,
	60%,
	80%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
	20% {
		transform: scale3d(1.1, 1.1, 1.1)
	}
	40% {
		transform: scale3d(.9, .9, .9)
	}
	60% {
		opacity: 1;
		transform: scale3d(1.03, 1.03, 1.03)
	}
	80% {
		transform: scale3d(.97, .97, .97)
	}
	to {
		opacity: 1;
		transform: scaleX(1)
	}
}

.bounceIn {
	animation-name: bounceIn
}

@-webkit-keyframes bounceInDown {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(0, -3000px, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(0, 25px, 0)
	}
	75% {
		transform: translate3d(0, -10px, 0)
	}
	90% {
		transform: translate3d(0, 5px, 0)
	}
	to {
		transform: none
	}
}

@keyframes bounceInDown {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(0, -3000px, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(0, 25px, 0)
	}
	75% {
		transform: translate3d(0, -10px, 0)
	}
	90% {
		transform: translate3d(0, 5px, 0)
	}
	to {
		transform: none
	}
}

.bounceInDown {
	animation-name: bounceInDown
}

@-webkit-keyframes bounceInLeft {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(-3000px, 0, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(25px, 0, 0)
	}
	75% {
		transform: translate3d(-10px, 0, 0)
	}
	90% {
		transform: translate3d(5px, 0, 0)
	}
	to {
		transform: none
	}
}

@keyframes bounceInLeft {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(-3000px, 0, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(25px, 0, 0)
	}
	75% {
		transform: translate3d(-10px, 0, 0)
	}
	90% {
		transform: translate3d(5px, 0, 0)
	}
	to {
		transform: none
	}
}

.bounceInLeft {
	animation-name: bounceInLeft
}

@-webkit-keyframes bounceInRight {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(3000px, 0, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(-25px, 0, 0)
	}
	75% {
		transform: translate3d(10px, 0, 0)
	}
	90% {
		transform: translate3d(-5px, 0, 0)
	}
	to {
		transform: none
	}
}

@keyframes bounceInRight {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(3000px, 0, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(-25px, 0, 0)
	}
	75% {
		transform: translate3d(10px, 0, 0)
	}
	90% {
		transform: translate3d(-5px, 0, 0)
	}
	to {
		transform: none
	}
}

.bounceInRight {
	animation-name: bounceInRight
}

@-webkit-keyframes bounceInUp {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(0, 3000px, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(0, -20px, 0)
	}
	75% {
		transform: translate3d(0, 10px, 0)
	}
	90% {
		transform: translate3d(0, -5px, 0)
	}
	to {
		transform: translateZ(0)
	}
}

@keyframes bounceInUp {
	0%,
	60%,
	75%,
	90%,
	to {
		animation-timing-function: cubic-bezier(.215, .61, .355, 1)
	}
	0% {
		opacity: 0;
		transform: translate3d(0, 3000px, 0)
	}
	60% {
		opacity: 1;
		transform: translate3d(0, -20px, 0)
	}
	75% {
		transform: translate3d(0, 10px, 0)
	}
	90% {
		transform: translate3d(0, -5px, 0)
	}
	to {
		transform: translateZ(0)
	}
}

.bounceInUp {
	animation-name: bounceInUp
}

@-webkit-keyframes bounceOut {
	20% {
		transform: scale3d(.9, .9, .9)
	}
	50%,
	55% {
		opacity: 1;
		transform: scale3d(1.1, 1.1, 1.1)
	}
	to {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
}

@keyframes bounceOut {
	20% {
		transform: scale3d(.9, .9, .9)
	}
	50%,
	55% {
		opacity: 1;
		transform: scale3d(1.1, 1.1, 1.1)
	}
	to {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
}

.bounceOut {
	animation-name: bounceOut
}

@-webkit-keyframes bounceOutDown {
	20% {
		transform: translate3d(0, 10px, 0)
	}
	40%,
	45% {
		opacity: 1;
		transform: translate3d(0, -20px, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
}

@keyframes bounceOutDown {
	20% {
		transform: translate3d(0, 10px, 0)
	}
	40%,
	45% {
		opacity: 1;
		transform: translate3d(0, -20px, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
}

.bounceOutDown {
	animation-name: bounceOutDown
}

@-webkit-keyframes bounceOutLeft {
	20% {
		opacity: 1;
		transform: translate3d(20px, 0, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
}

@keyframes bounceOutLeft {
	20% {
		opacity: 1;
		transform: translate3d(20px, 0, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
}

.bounceOutLeft {
	animation-name: bounceOutLeft
}

@-webkit-keyframes bounceOutRight {
	20% {
		opacity: 1;
		transform: translate3d(-20px, 0, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
}

@keyframes bounceOutRight {
	20% {
		opacity: 1;
		transform: translate3d(-20px, 0, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
}

.bounceOutRight {
	animation-name: bounceOutRight
}

@-webkit-keyframes bounceOutUp {
	20% {
		transform: translate3d(0, -10px, 0)
	}
	40%,
	45% {
		opacity: 1;
		transform: translate3d(0, 20px, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
}

@keyframes bounceOutUp {
	20% {
		transform: translate3d(0, -10px, 0)
	}
	40%,
	45% {
		opacity: 1;
		transform: translate3d(0, 20px, 0)
	}
	to {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
}

.bounceOutUp {
	animation-name: bounceOutUp
}

@-webkit-keyframes fadeIn {
	0% {
		opacity: 0
	}
	to {
		opacity: 1
	}
}

@keyframes fadeIn {
	0% {
		opacity: 0
	}
	to {
		opacity: 1
	}
}

.fadeIn {
	animation-name: fadeIn
}

@-webkit-keyframes fadeInDown {
	0% {
		opacity: 0;
		transform: translate3d(0, -100%, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInDown {
	0% {
		opacity: 0;
		transform: translate3d(0, -100%, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInDown {
	animation-name: fadeInDown
}

@-webkit-keyframes fadeInDownBig {
	0% {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInDownBig {
	0% {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInDownBig {
	animation-name: fadeInDownBig
}

@-webkit-keyframes fadeInLeft {
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInLeft {
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInLeft {
	animation-name: fadeInLeft
}

@-webkit-keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInLeftBig {
	animation-name: fadeInLeftBig
}

@-webkit-keyframes fadeInRight {
	0% {
		opacity: 0;
		transform: translate3d(100%, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInRight {
	0% {
		opacity: 0;
		transform: translate3d(100%, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInRight {
	animation-name: fadeInRight
}

@-webkit-keyframes fadeInRightBig {
	0% {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInRightBig {
	0% {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInRightBig {
	animation-name: fadeInRightBig
}

@-webkit-keyframes fadeInUp {
	0% {
		opacity: 0;
		transform: translate3d(0, 100%, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInUp {
	0% {
		opacity: 0;
		transform: translate3d(0, 100%, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInUp {
	animation-name: fadeInUp
}

@-webkit-keyframes fadeInUpBig {
	0% {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes fadeInUpBig {
	0% {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.fadeInUpBig {
	animation-name: fadeInUpBig
}

@-webkit-keyframes fadeOut {
	0% {
		opacity: 1
	}
	to {
		opacity: 0
	}
}

@keyframes fadeOut {
	0% {
		opacity: 1
	}
	to {
		opacity: 0
	}
}

.fadeOut {
	animation-name: fadeOut
}

@-webkit-keyframes fadeOutDown {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, 100%, 0)
	}
}

@keyframes fadeOutDown {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, 100%, 0)
	}
}

.fadeOutDown {
	animation-name: fadeOutDown
}

@-webkit-keyframes fadeOutDownBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
}

@keyframes fadeOutDownBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, 2000px, 0)
	}
}

.fadeOutDownBig {
	animation-name: fadeOutDownBig
}

@-webkit-keyframes fadeOutLeft {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(-100%, 0, 0)
	}
}

@keyframes fadeOutLeft {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(-100%, 0, 0)
	}
}

.fadeOutLeft {
	animation-name: fadeOutLeft
}

@-webkit-keyframes fadeOutLeftBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
}

@keyframes fadeOutLeftBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(-2000px, 0, 0)
	}
}

.fadeOutLeftBig {
	animation-name: fadeOutLeftBig
}

@-webkit-keyframes fadeOutRight {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(100%, 0, 0)
	}
}

@keyframes fadeOutRight {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(100%, 0, 0)
	}
}

.fadeOutRight {
	animation-name: fadeOutRight
}

@-webkit-keyframes fadeOutRightBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
}

@keyframes fadeOutRightBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(2000px, 0, 0)
	}
}

.fadeOutRightBig {
	animation-name: fadeOutRightBig
}

@-webkit-keyframes fadeOutUp {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, -100%, 0)
	}
}

@keyframes fadeOutUp {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, -100%, 0)
	}
}

.fadeOutUp {
	animation-name: fadeOutUp
}

@-webkit-keyframes fadeOutUpBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
}

@keyframes fadeOutUpBig {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(0, -2000px, 0)
	}
}

.fadeOutUpBig {
	animation-name: fadeOutUpBig
}

@-webkit-keyframes flip {
	0% {
		transform: perspective(400px) rotateY(-1turn)
	}
	0%,
	40% {
		animation-timing-function: ease-out
	}
	40% {
		transform: perspective(400px) translateZ(150px) rotateY(-190deg)
	}
	50% {
		transform: perspective(400px) translateZ(150px) rotateY(-170deg)
	}
	50%,
	80% {
		animation-timing-function: ease-in
	}
	80% {
		transform: perspective(400px) scale3d(.95, .95, .95)
	}
	to {
		transform: perspective(400px);
		animation-timing-function: ease-in
	}
}

@keyframes flip {
	0% {
		transform: perspective(400px) rotateY(-1turn)
	}
	0%,
	40% {
		animation-timing-function: ease-out
	}
	40% {
		transform: perspective(400px) translateZ(150px) rotateY(-190deg)
	}
	50% {
		transform: perspective(400px) translateZ(150px) rotateY(-170deg)
	}
	50%,
	80% {
		animation-timing-function: ease-in
	}
	80% {
		transform: perspective(400px) scale3d(.95, .95, .95)
	}
	to {
		transform: perspective(400px);
		animation-timing-function: ease-in
	}
}

.animated.flip {
	backface-visibility: visible;
	animation-name: flip
}

@-webkit-keyframes flipInX {
	0% {
		transform: perspective(400px) rotateX(90deg);
		opacity: 0
	}
	0%,
	40% {
		animation-timing-function: ease-in
	}
	40% {
		transform: perspective(400px) rotateX(-20deg)
	}
	60% {
		transform: perspective(400px) rotateX(10deg);
		opacity: 1
	}
	80% {
		transform: perspective(400px) rotateX(-5deg)
	}
	to {
		transform: perspective(400px)
	}
}

@keyframes flipInX {
	0% {
		transform: perspective(400px) rotateX(90deg);
		opacity: 0
	}
	0%,
	40% {
		animation-timing-function: ease-in
	}
	40% {
		transform: perspective(400px) rotateX(-20deg)
	}
	60% {
		transform: perspective(400px) rotateX(10deg);
		opacity: 1
	}
	80% {
		transform: perspective(400px) rotateX(-5deg)
	}
	to {
		transform: perspective(400px)
	}
}

.flipInX {
	backface-visibility: visible!important;
	animation-name: flipInX
}

@-webkit-keyframes flipInY {
	0% {
		transform: perspective(400px) rotateY(90deg);
		opacity: 0
	}
	0%,
	40% {
		animation-timing-function: ease-in
	}
	40% {
		transform: perspective(400px) rotateY(-20deg)
	}
	60% {
		transform: perspective(400px) rotateY(10deg);
		opacity: 1
	}
	80% {
		transform: perspective(400px) rotateY(-5deg)
	}
	to {
		transform: perspective(400px)
	}
}

@keyframes flipInY {
	0% {
		transform: perspective(400px) rotateY(90deg);
		opacity: 0
	}
	0%,
	40% {
		animation-timing-function: ease-in
	}
	40% {
		transform: perspective(400px) rotateY(-20deg)
	}
	60% {
		transform: perspective(400px) rotateY(10deg);
		opacity: 1
	}
	80% {
		transform: perspective(400px) rotateY(-5deg)
	}
	to {
		transform: perspective(400px)
	}
}

.flipInY {
	backface-visibility: visible!important;
	animation-name: flipInY
}

@-webkit-keyframes flipOutX {
	0% {
		transform: perspective(400px)
	}
	30% {
		transform: perspective(400px) rotateX(-20deg);
		opacity: 1
	}
	to {
		transform: perspective(400px) rotateX(90deg);
		opacity: 0
	}
}

@keyframes flipOutX {
	0% {
		transform: perspective(400px)
	}
	30% {
		transform: perspective(400px) rotateX(-20deg);
		opacity: 1
	}
	to {
		transform: perspective(400px) rotateX(90deg);
		opacity: 0
	}
}

.flipOutX {
	animation-name: flipOutX;
	backface-visibility: visible!important
}

@-webkit-keyframes flipOutY {
	0% {
		transform: perspective(400px)
	}
	30% {
		transform: perspective(400px) rotateY(-15deg);
		opacity: 1
	}
	to {
		transform: perspective(400px) rotateY(90deg);
		opacity: 0
	}
}

@keyframes flipOutY {
	0% {
		transform: perspective(400px)
	}
	30% {
		transform: perspective(400px) rotateY(-15deg);
		opacity: 1
	}
	to {
		transform: perspective(400px) rotateY(90deg);
		opacity: 0
	}
}

.flipOutY {
	backface-visibility: visible!important;
	animation-name: flipOutY
}

@-webkit-keyframes lightSpeedIn {
	0% {
		transform: translate3d(100%, 0, 0) skewX(-30deg);
		opacity: 0
	}
	60% {
		transform: skewX(20deg)
	}
	60%,
	80% {
		opacity: 1
	}
	80% {
		transform: skewX(-5deg)
	}
	to {
		transform: none;
		opacity: 1
	}
}

@keyframes lightSpeedIn {
	0% {
		transform: translate3d(100%, 0, 0) skewX(-30deg);
		opacity: 0
	}
	60% {
		transform: skewX(20deg)
	}
	60%,
	80% {
		opacity: 1
	}
	80% {
		transform: skewX(-5deg)
	}
	to {
		transform: none;
		opacity: 1
	}
}

.lightSpeedIn {
	animation-name: lightSpeedIn;
	animation-timing-function: ease-out
}

@-webkit-keyframes lightSpeedOut {
	0% {
		opacity: 1
	}
	to {
		transform: translate3d(100%, 0, 0) skewX(30deg);
		opacity: 0
	}
}

@keyframes lightSpeedOut {
	0% {
		opacity: 1
	}
	to {
		transform: translate3d(100%, 0, 0) skewX(30deg);
		opacity: 0
	}
}

.lightSpeedOut {
	animation-name: lightSpeedOut;
	animation-timing-function: ease-in
}

@-webkit-keyframes rotateIn {
	0% {
		transform-origin: center;
		transform: rotate(-200deg);
		opacity: 0
	}
	to {
		transform-origin: center;
		transform: none;
		opacity: 1
	}
}

@keyframes rotateIn {
	0% {
		transform-origin: center;
		transform: rotate(-200deg);
		opacity: 0
	}
	to {
		transform-origin: center;
		transform: none;
		opacity: 1
	}
}

.rotateIn {
	animation-name: rotateIn
}

@-webkit-keyframes rotateInDownLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
	to {
		transform-origin: left bottom;
		transform: none;
		opacity: 1
	}
}

@keyframes rotateInDownLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
	to {
		transform-origin: left bottom;
		transform: none;
		opacity: 1
	}
}

.rotateInDownLeft {
	animation-name: rotateInDownLeft
}

@-webkit-keyframes rotateInDownRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(45deg);
		opacity: 0
	}
	to {
		transform-origin: right bottom;
		transform: none;
		opacity: 1
	}
}

@keyframes rotateInDownRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(45deg);
		opacity: 0
	}
	to {
		transform-origin: right bottom;
		transform: none;
		opacity: 1
	}
}

.rotateInDownRight {
	animation-name: rotateInDownRight
}

@-webkit-keyframes rotateInUpLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(45deg);
		opacity: 0
	}
	to {
		transform-origin: left bottom;
		transform: none;
		opacity: 1
	}
}

@keyframes rotateInUpLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(45deg);
		opacity: 0
	}
	to {
		transform-origin: left bottom;
		transform: none;
		opacity: 1
	}
}

.rotateInUpLeft {
	animation-name: rotateInUpLeft
}

@-webkit-keyframes rotateInUpRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(-90deg);
		opacity: 0
	}
	to {
		transform-origin: right bottom;
		transform: none;
		opacity: 1
	}
}

@keyframes rotateInUpRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(-90deg);
		opacity: 0
	}
	to {
		transform-origin: right bottom;
		transform: none;
		opacity: 1
	}
}

.rotateInUpRight {
	animation-name: rotateInUpRight
}

@-webkit-keyframes rotateOut {
	0% {
		transform-origin: center;
		opacity: 1
	}
	to {
		transform-origin: center;
		transform: rotate(200deg);
		opacity: 0
	}
}

@keyframes rotateOut {
	0% {
		transform-origin: center;
		opacity: 1
	}
	to {
		transform-origin: center;
		transform: rotate(200deg);
		opacity: 0
	}
}

.rotateOut {
	animation-name: rotateOut
}

@-webkit-keyframes rotateOutDownLeft {
	0% {
		transform-origin: left bottom;
		opacity: 1
	}
	to {
		transform-origin: left bottom;
		transform: rotate(45deg);
		opacity: 0
	}
}

@keyframes rotateOutDownLeft {
	0% {
		transform-origin: left bottom;
		opacity: 1
	}
	to {
		transform-origin: left bottom;
		transform: rotate(45deg);
		opacity: 0
	}
}

.rotateOutDownLeft {
	animation-name: rotateOutDownLeft
}

@-webkit-keyframes rotateOutDownRight {
	0% {
		transform-origin: right bottom;
		opacity: 1
	}
	to {
		transform-origin: right bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
}

@keyframes rotateOutDownRight {
	0% {
		transform-origin: right bottom;
		opacity: 1
	}
	to {
		transform-origin: right bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
}

.rotateOutDownRight {
	animation-name: rotateOutDownRight
}

@-webkit-keyframes rotateOutUpLeft {
	0% {
		transform-origin: left bottom;
		opacity: 1
	}
	to {
		transform-origin: left bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
}

@keyframes rotateOutUpLeft {
	0% {
		transform-origin: left bottom;
		opacity: 1
	}
	to {
		transform-origin: left bottom;
		transform: rotate(-45deg);
		opacity: 0
	}
}

.rotateOutUpLeft {
	animation-name: rotateOutUpLeft
}

@-webkit-keyframes rotateOutUpRight {
	0% {
		transform-origin: right bottom;
		opacity: 1
	}
	to {
		transform-origin: right bottom;
		transform: rotate(90deg);
		opacity: 0
	}
}

@keyframes rotateOutUpRight {
	0% {
		transform-origin: right bottom;
		opacity: 1
	}
	to {
		transform-origin: right bottom;
		transform: rotate(90deg);
		opacity: 0
	}
}

.rotateOutUpRight {
	animation-name: rotateOutUpRight
}

@-webkit-keyframes hinge {
	0% {
		transform-origin: top left
	}
	0%,
	20%,
	60% {
		animation-timing-function: ease-in-out
	}
	20%,
	60% {
		transform: rotate(80deg);
		transform-origin: top left
	}
	40%,
	80% {
		transform: rotate(60deg);
		transform-origin: top left;
		animation-timing-function: ease-in-out;
		opacity: 1
	}
	to {
		transform: translate3d(0, 700px, 0);
		opacity: 0
	}
}

@keyframes hinge {
	0% {
		transform-origin: top left
	}
	0%,
	20%,
	60% {
		animation-timing-function: ease-in-out
	}
	20%,
	60% {
		transform: rotate(80deg);
		transform-origin: top left
	}
	40%,
	80% {
		transform: rotate(60deg);
		transform-origin: top left;
		animation-timing-function: ease-in-out;
		opacity: 1
	}
	to {
		transform: translate3d(0, 700px, 0);
		opacity: 0
	}
}

.hinge {
	animation-name: hinge
}

@-webkit-keyframes rollIn {
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0) rotate(-120deg)
	}
	to {
		opacity: 1;
		transform: none
	}
}

@keyframes rollIn {
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0) rotate(-120deg)
	}
	to {
		opacity: 1;
		transform: none
	}
}

.rollIn {
	animation-name: rollIn
}

@-webkit-keyframes rollOut {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(100%, 0, 0) rotate(120deg)
	}
}

@keyframes rollOut {
	0% {
		opacity: 1
	}
	to {
		opacity: 0;
		transform: translate3d(100%, 0, 0) rotate(120deg)
	}
}

.rollOut {
	animation-name: rollOut
}

@-webkit-keyframes zoomIn {
	0% {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
	50% {
		opacity: 1
	}
}

@keyframes zoomIn {
	0% {
		opacity: 0;
		transform: scale3d(.3, .3, .3)
	}
	50% {
		opacity: 1
	}
}

.zoomIn {
	animation-name: zoomIn
}

@-webkit-keyframes zoomInDown {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomInDown {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomInDown {
	animation-name: zoomInDown
}

@-webkit-keyframes zoomInLeft {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomInLeft {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomInLeft {
	animation-name: zoomInLeft
}

@-webkit-keyframes zoomInRight {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomInRight {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomInRight {
	animation-name: zoomInRight
}

@-webkit-keyframes zoomInUp {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomInUp {
	0% {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	60% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomInUp {
	animation-name: zoomInUp
}

@-webkit-keyframes zoomOut {
	0% {
		opacity: 1
	}
	50% {
		transform: scale3d(.3, .3, .3)
	}
	50%,
	to {
		opacity: 0
	}
}

@keyframes zoomOut {
	0% {
		opacity: 1
	}
	50% {
		transform: scale3d(.3, .3, .3)
	}
	50%,
	to {
		opacity: 0
	}
}

.zoomOut {
	animation-name: zoomOut
}

@-webkit-keyframes zoomOutDown {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	to {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
		transform-origin: center bottom;
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomOutDown {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	to {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
		transform-origin: center bottom;
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomOutDown {
	animation-name: zoomOutDown
}

@-webkit-keyframes zoomOutLeft {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0)
	}
	to {
		opacity: 0;
		transform: scale(.1) translate3d(-2000px, 0, 0);
		transform-origin: left center
	}
}

@keyframes zoomOutLeft {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0)
	}
	to {
		opacity: 0;
		transform: scale(.1) translate3d(-2000px, 0, 0);
		transform-origin: left center
	}
}

.zoomOutLeft {
	animation-name: zoomOutLeft
}

@-webkit-keyframes zoomOutRight {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0)
	}
	to {
		opacity: 0;
		transform: scale(.1) translate3d(2000px, 0, 0);
		transform-origin: right center
	}
}

@keyframes zoomOutRight {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0)
	}
	to {
		opacity: 0;
		transform: scale(.1) translate3d(2000px, 0, 0);
		transform-origin: right center
	}
}

.zoomOutRight {
	animation-name: zoomOutRight
}

@-webkit-keyframes zoomOutUp {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	to {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
		transform-origin: center bottom;
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

@keyframes zoomOutUp {
	40% {
		opacity: 1;
		transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
		animation-timing-function: cubic-bezier(.55, .055, .675, .19)
	}
	to {
		opacity: 0;
		transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
		transform-origin: center bottom;
		animation-timing-function: cubic-bezier(.175, .885, .32, 1)
	}
}

.zoomOutUp {
	animation-name: zoomOutUp
}

@-webkit-keyframes slideInDown {
	0% {
		transform: translate3d(0, -100%, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

@keyframes slideInDown {
	0% {
		transform: translate3d(0, -100%, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

.slideInDown {
	animation-name: slideInDown
}

@-webkit-keyframes slideInLeft {
	0% {
		transform: translate3d(-100%, 0, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

@keyframes slideInLeft {
	0% {
		transform: translate3d(-100%, 0, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

.slideInLeft {
	animation-name: slideInLeft
}

@-webkit-keyframes slideInRight {
	0% {
		transform: translate3d(100%, 0, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

@keyframes slideInRight {
	0% {
		transform: translate3d(100%, 0, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

.slideInRight {
	animation-name: slideInRight
}

@-webkit-keyframes slideInUp {
	0% {
		transform: translate3d(0, 100%, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

@keyframes slideInUp {
	0% {
		transform: translate3d(0, 100%, 0);
		visibility: visible
	}
	to {
		transform: translateZ(0)
	}
}

.slideInUp {
	animation-name: slideInUp
}

@-webkit-keyframes slideOutDown {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(0, 100%, 0)
	}
}

@keyframes slideOutDown {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(0, 100%, 0)
	}
}

.slideOutDown {
	animation-name: slideOutDown
}

@-webkit-keyframes slideOutLeft {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(-100%, 0, 0)
	}
}

.slideOutLeft {
	animation-name: slideOutLeft
}

@-webkit-keyframes slideOutRight {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(100%, 0, 0)
	}
}

@keyframes slideOutRight {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(100%, 0, 0)
	}
}

.slideOutRight {
	animation-name: slideOutRight
}

@-webkit-keyframes slideOutUp {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(0, -100%, 0)
	}
}

@keyframes slideOutUp {
	0% {
		transform: translateZ(0)
	}
	to {
		visibility: hidden;
		transform: translate3d(0, -100%, 0)
	}
}

.banner-page .box-main {
	/*! background-color:#fff; */
	padding-bottom: 10px
}

.box-main {
	/*! background-color:#fff; */
	text-align: center;
	padding: 15px
}

.box-main h4 {
	font-size: 26px;
	color: #fff;
	margin-bottom: 30px
}

.box-main .blog-item {
	text-align: left
}

.box-main .blog-item .page-header {
	margin: 0 0 20px
}

.box-main .link-details {
	padding: 0 10px
}

.box-main .link-details .link-image {
	max-height: 200px
}

.box-main .link-details .link-title {
	text-transform: none
}

body.interstitial-page {
	overflow: hidden;
	padding-top: 0
}

.interstitial-page #mainNav {
	margin-bottom: 0
}

.interstitial-page .navbar-default {
	padding: 5px 0
}

.interstitial-page .skip-ad a {
	color: #1e70dc;
	padding: 13px 52px 13px 20px;
	border-radius: 20px;
	background-image: url(../img/skip-ad.png);
	background-color: #fff;
	background-position: right 11px bottom 50%;
	background-repeat: no-repeat;
	background-size: 34px
}

.blog-item .page-header h3 {
	text-transform: none
}

footer a {
	color: #fff
}

footer a.active,
footer a:active,
footer a:focus,
footer a:hover {
	color: #f7f6f6
}

footer .payment-methods {
	padding: 30px 0 25px
}

@media (min-width:768px) {
	footer .payment-methods {
		padding: 45px 0 40px
	}
}

footer .copyright-container {
	padding: 40px 0;
	font-size: 13px
}

footer .copyright div {
	padding-top: 3px;
	margin-bottom: -10px
}

footer .bottom-menu ul {
	padding-top: 3px;
	margin-bottom: 0
}

footer .social-links ul {
	margin-bottom: 0
}

footer .social-links ul li a {
	display: block;
	text-align: center;
	background-color: #fff;
	height: 30px;
	width: 30px;
	border-radius: 100%;
	font-size: 13px;
	line-height: 25px;
	color: #101417;
	outline: 0;
	transition: all .3s;
	padding: 2px
}

footer .social-links ul li a:active,
footer .social-links ul li a:focus,
footer .social-links ul li a:hover {
	background-color: #f7f6f6
}

footer .bottom-menu,
footer .copyright,
footer .social-links {
	text-align: center
}

@media (min-width:768px) {
	footer .social-links {
		text-align: center
	}
}

.form-control {
	display: block;
	width: 100%;
	padding: .65rem;
	font-size: 14px;
	line-height: 1.42857143;
	color: #fff;
	background-color: #282f4a;
	background-image: none;
	border: 2px solid #282f4a;
	border-radius: 5px;
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}

.form-control:focus {
	border-color: #00ea8a;
	outline: 0
}

.form-control::-moz-placeholder {
	color: color:#505050;
	opacity: 1
}

.form-control:-ms-input-placeholder {
	color: #999
}

.form-control::-webkit-input-placeholder {
	color: color:#505050
}

.form-control::-ms-expand {
	background-color: transparent;
	border: 0
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
	background-color: #fff;
	border: 0;
	color: #43484f;
	font-weight: 400
}

.form-control[disabled],
fieldset[disabled] .form-control {
	cursor: not-allowed
}

textarea.form-control {
	height: auto
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
	input[type=date].form-control,
	input[type=datetime-local].form-control,
	input[type=month].form-control,
	input[type=time].form-control {
		line-height: 34px
	}
	.input-group-sm input[type=date],
	.input-group-sm input[type=datetime-local],
	.input-group-sm input[type=month],
	.input-group-sm input[type=time],
	input[type=date].input-sm,
	input[type=datetime-local].input-sm,
	input[type=month].input-sm,
	input[type=time].input-sm {
		line-height: 30px
	}
	.input-group-lg input[type=date],
	.input-group-lg input[type=datetime-local],
	.input-group-lg input[type=month],
	.input-group-lg input[type=time],
	input[type=date].input-lg,
	input[type=datetime-local].input-lg,
	input[type=month].input-lg,
	input[type=time].input-lg {
		line-height: 46px
	}
}

.form-group {
	margin-bottom: 10px
}

.pad {
	padding-left: 15px;
	padding-right: 15px
}

.checkbox,
.radio {
	position: relative;
	display: block;
	margin-top: 10px;
	margin-bottom: 10px
}

.checkbox label,
.radio label {
	min-height: 20px;
	padding-left: 20px;
	margin-bottom: 0;
	font-weight: 400;
	cursor: pointer
}

.checkbox input[type=checkbox],
.checkbox-inline input[type=checkbox],
.radio input[type=radio],
.radio-inline input[type=radio] {
	position: absolute;
	margin-left: -20px
}

.checkbox+.checkbox,
.radio+.radio {
	margin-top: -5px
}

.checkbox-inline,
.radio-inline {
	position: relative;
	display: inline-block;
	padding-left: 20px;
	margin-bottom: 0;
	font-weight: 400;
	vertical-align: middle;
	cursor: pointer
}

.checkbox-inline+.checkbox-inline,
.radio-inline+.radio-inline {
	margin-top: 0;
	margin-left: 10px
}

fieldset[disabled] input[type=checkbox],
fieldset[disabled] input[type=radio],
input[type=checkbox].disabled,
input[type=checkbox][disabled],
input[type=radio].disabled,
input[type=radio][disabled] {
	cursor: not-allowed
}

.checkbox-inline.disabled,
.radio-inline.disabled,
fieldset[disabled] .checkbox-inline,
fieldset[disabled] .radio-inline {
	cursor: not-allowed
}

.checkbox.disabled label,
.radio.disabled label,
fieldset[disabled] .checkbox label,
fieldset[disabled] .radio label {
	cursor: not-allowed
}

.form-control-static {
	min-height: 34px;
	padding-top: 7px;
	padding-bottom: 7px;
	margin-bottom: 0
}

.form-control-static.input-lg,
.form-control-static.input-sm {
	padding-right: 0;
	padding-left: 0
}

.input-sm {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

select.input-sm {
	height: 30px;
	line-height: 30px
}

select[multiple].input-sm,
textarea.input-sm {
	height: auto
}

.form-group-sm .form-control {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}

.form-group-sm select.form-control {
	height: 30px;
	line-height: 30px
}

.form-group-sm select[multiple].form-control,
.form-group-sm textarea.form-control {
	height: auto
}

.form-group-sm .form-control-static {
	height: 30px;
	min-height: 32px;
	padding: 6px 10px;
	font-size: 12px;
	line-height: 1.5
}

.input-lg {
	height: 46px;
	padding: 10px 16px;
	font-size: 15px;
	line-height: 1.3333333;
	border-radius: 6px
}

select.input-lg {
	height: 46px;
	line-height: 46px
}

select[multiple].input-lg,
textarea.input-lg {
	height: auto
}

.form-group-lg .form-control {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}

.form-group-lg select.form-control {
	height: 46px;
	line-height: 46px
}

.form-group-lg select[multiple].form-control,
.form-group-lg textarea.form-control {
	height: auto
}

.form-group-lg .form-control-static {
	height: 46px;
	min-height: 38px;
	padding: 11px 16px;
	font-size: 18px;
	line-height: 1.3333333
}

.has-feedback {
	position: relative
}

.has-feedback .form-control {
	padding-right: 42.5px
}

.form-control-feedback {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 2;
	display: block;
	width: 34px;
	height: 34px;
	line-height: 34px;
	text-align: center
}

.form-group-lg .form-control+.form-control-feedback,
.input-group-lg+.form-control-feedback,
.input-lg+.form-control-feedback {
	width: 46px;
	height: 46px;
	line-height: 46px
}

.form-group-sm .form-control+.form-control-feedback,
.input-group-sm+.form-control-feedback,
.input-sm+.form-control-feedback {
	width: 30px;
	height: 30px;
	line-height: 30px
}

.has-success .checkbox,
.has-success .checkbox-inline,
.has-success .control-label,
.has-success .help-block,
.has-success .radio,
.has-success .radio-inline,
.has-success.checkbox label,
.has-success.checkbox-inline label,
.has-success.radio label,
.has-success.radio-inline label {
	color: #3c763d
}

.has-success .form-control {
	border-color: #3c763d;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-success .form-control:focus {
	border-color: #2b542c;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #67b168
}

.has-success .input-group-addon {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #3c763d
}

.has-success .form-control-feedback {
	color: #3c763d
}

.has-warning .checkbox,
.has-warning .checkbox-inline,
.has-warning .control-label,
.has-warning .help-block,
.has-warning .radio,
.has-warning .radio-inline,
.has-warning.checkbox label,
.has-warning.checkbox-inline label,
.has-warning.radio label,
.has-warning.radio-inline label {
	color: #8a6d3b
}

.has-warning .form-control {
	border-color: #8a6d3b;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-warning .form-control:focus {
	border-color: #66512c;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b
}

.has-warning .input-group-addon {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #8a6d3b
}

.has-warning .form-control-feedback {
	color: #8a6d3b
}

.has-error .checkbox,
.has-error .checkbox-inline,
.has-error .control-label,
.has-error .help-block,
.has-error .radio,
.has-error .radio-inline,
.has-error.checkbox label,
.has-error.checkbox-inline label,
.has-error.radio label,
.has-error.radio-inline label {
	color: #a94442
}

.has-error .form-control {
	border-color: #a94442;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-error .form-control:focus {
	border-color: #843534;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483
}

.has-error .input-group-addon {
	color: #a94442;
	background-color: #f2dede;
	border-color: #a94442
}

.has-error .form-control-feedback {
	color: #a94442
}

.has-feedback label~.form-control-feedback {
	top: 25px
}

.has-feedback label.sr-only~.form-control-feedback {
	top: 0
}

.help-block {
	display: block;
	margin-top: 0;
	margin-bottom: 0;
	color: #737373
}

@media (min-width:768px) {
	.form-inline .form-group {
		display: inline-block;
		margin-bottom: 0;
		vertical-align: middle
	}
	.form-inline .form-control {
		display: inline-block;
		width: auto;
		vertical-align: middle
	}
	.form-inline .form-control-static {
		display: inline-block
	}
	.form-inline .input-group {
		display: inline-table;
		vertical-align: middle
	}
	.form-inline .input-group .form-control,
	.form-inline .input-group .input-group-addon,
	.form-inline .input-group .input-group-btn {
		width: auto
	}
	.form-inline .input-group>.form-control {
		width: 100%
	}
	.form-inline .control-label {
		margin-bottom: 0;
		vertical-align: middle
	}
	.form-inline .checkbox,
	.form-inline .radio {
		display: inline-block;
		margin-top: 0;
		margin-bottom: 0;
		vertical-align: middle
	}
	.form-inline .checkbox label,
	.form-inline .radio label {
		padding-left: 0
	}
	.form-inline .checkbox input[type=checkbox],
	.form-inline .radio input[type=radio] {
		position: relative;
		margin-left: 0
	}
	.form-inline .has-feedback .form-control-feedback {
		top: 0
	}
}

.form-horizontal .checkbox,
.form-horizontal .checkbox-inline,
.form-horizontal .radio,
.form-horizontal .radio-inline {
	padding-top: 7px;
	margin-top: 0;
	margin-bottom: 0
}

.form-horizontal .checkbox,
.form-horizontal .radio {
	min-height: 27px
}

.form-horizontal .form-group {
	margin-right: -15px;
	margin-left: -15px
}

@media (min-width:768px) {
	.form-horizontal .control-label {
		padding-top: 7px;
		margin-bottom: 0;
		text-align: right
	}
}

.form-horizontal .has-feedback .form-control-feedback {
	right: 15px
}

@media (min-width:768px) {
	.form-horizontal .form-group-lg .control-label {
		padding-top: 11px;
		font-size: 18px
	}
}

@media (min-width:768px) {
	.form-horizontal .form-group-sm .control-label {
		padding-top: 6px;
		font-size: 12px
	}
}

.r-bg {
	position: relative;
	height: 300px;
	display: block/*! background:#121D33; */
}

.r-bg:before {
	content: '';
	position: absolute;
	background: #162239;
	top: -150px;
	bottom: 0;
	right: 0;
	left: 0;
	width: 100%;
	background-repeat: no-repeat;
	background-size: cover;
	transform: skewY(-3deg);
	margin-bottom: 35px
}

.rates {
	position: relative;
	display: block;
	padding-bottom: 0/*! background:#fff */
}

.short {
	padding-bottom: 10px;
	/*! background:#fff; */
	position: relative;
	display: block
}

.short .content {
	border-radius: 3px;
	-webkit-box-shadow: 0 0 50px 5px rgba(0, 0, 0, .29);
	box-shadow: 0 0 50px 5px rgba(0, 0, 0, .29);
	margin-bottom: 30px;
	padding: 30px 20px;
	text-align: left;
	position: relative;
	margin-top: -300px;
	display: block;
	height: auto
}

.content {
	min-height: 100%;
	height: 100%;
	background: #162239;
	position: relative;
	z-index: 0
}

.col-md-8 .faq {
	text-align: left;
	padding-top: 80px
}

.box-main .faq {
	text-align: left;
	margin: 15px;
	padding-top: 80px
}

.content .faq {
	margin-top: -20px;
	color: #00ea8a
}

.faq h4,
.link .faq h4 {
	font-size: 20px;
	font-weight: 500;
	position: relative;
	display: block;
	margin-bottom: 10px;
	margin-top: 20px;
	color: #00ea8a
}

.faq p,
.link .faq p {
	display: block;
	font-weight: 400;
	color: #777!important;
	font-size: 15px
}

.link {
	position: relative;
	padding-bottom: 50px;
	/*! background:#fff; */
	display: block
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
	display: block
}

audio,
canvas,
progress,
video {
	display: inline-block;
	vertical-align: baseline
}

audio:not([controls]) {
	display: none;
	height: 0
}

[hidden],
template {
	display: none
}

a {
	background-color: transparent
}

a:active,
a:hover {
	outline: 0
}

abbr[title] {
	border-bottom: 1px dotted
}

b,
strong {
	font-weight: 700
}

dfn {
	font-style: italic
}

h1 {
	margin: .67em 0;
	font-size: 2em
}

mark {
	color: #000;
	background: #ff0
}

small {
	font-size: 80%
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline
}

sup {
	top: -.5em
}

sub {
	bottom: -.25em
}

img {
	border: 0
}

svg:not(:root) {
	overflow: hidden
}

figure {
	margin: 1em 40px
}

hr {
	height: 0;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box
}

pre {
	overflow: auto
}

code,
kbd,
pre,
samp {
	font-family: monospace, monospace;
	font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	font: inherit;
	color: inherit
}

button {
	overflow: visible
}

button,
select {
	text-transform: none
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
	-webkit-appearance: button;
	cursor: pointer
}

button[disabled],
html input[disabled] {
	cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	padding: 0;
	border: 0
}

input {
	line-height: normal
}

input[type=checkbox],
input[type=radio] {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
	height: auto
}

input[type=search] {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	-webkit-appearance: textfield
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
	-webkit-appearance: none
}

fieldset {
	padding: .35em .625em .75em;
	margin: 0 2px;
	border: 1px solid silver
}

legend {
	padding: 0;
	border: 0
}

textarea {
	overflow: auto
}

optgroup {
	font-weight: 700
}

table {
	border-spacing: 0;
	border-collapse: collapse;
	border-top: 1px csolid red
}

td,
th {
	padding: 0
}

@media print {
	*,
	:after,
	:before {
		color: #000!important;
		text-shadow: none!important;
		background: 0 0!important;
		-webkit-box-shadow: none!important;
		box-shadow: none!important
	}
	a,
	a:visited {
		text-decoration: underline
	}
	a[href]:after {
		content: " (" attr(href) ")"
	}
	abbr[title]:after {
		content: " (" attr(title) ")"
	}
	a[href^="#"]:after,
	a[href^="javascript:"]:after {
		content: ""
	}
	blockquote,
	pre {
		border: 1px solid #999;
		page-break-inside: avoid
	}
	thead {
		display: table-header-group
	}
	img,
	tr {
		page-break-inside: avoid
	}
	img {
		max-width: 100%!important
	}
	h2,
	h3,
	p {
		orphans: 3;
		widows: 3
	}
	h2,
	h3 {
		page-break-after: avoid
	}
	.navbar {
		display: none
	}
	.btn>.caret,
	.dropup>.btn>.caret {
		border-top-color: #000!important
	}
	.label {
		border: 1px solid #000
	}
	.table {
		border-collapse: collapse!important
	}
	.table td,
	.table th {
		background-color: #fff!important
	}
	.table-bordered td,
	.table-bordered th {
		border: 1px solid #ddd!important
	}
}

* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}

:after,
:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: inherit;
	line-height: inherit
}

a {
	color: #66dbab;
	text-decoration: none
}

a:focus,
a:hover {
	color: #00ea8a;
	text-decoration: underline
}

a:focus {
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}

figure {
	margin: 0
}

img {
	vertical-align: middle
}

.carousel-inner>.item>a>img,
.carousel-inner>.item>img,
.img-responsive,
.thumbnail a>img,
.thumbnail>img {
	display: block;
	max-width: 100%;
	height: auto
}

.img-rounded {
	border-radius: 6px
}

.img-thumbnail {
	display: inline-block;
	max-width: 100%;
	height: auto;
	padding: 4px;
	line-height: 1.42857143;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	-webkit-transition: all .2s ease-in-out;
	-o-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out
}

.img-circle {
	border-radius: 50%
}

hr {
	margin-top: 20px;
	margin-bottom: 20px;
	border: 0;
	border-top: 1px solid #eee
}

.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
	position: static;
	width: auto;
	height: auto;
	margin: 0;
	overflow: visible;
	clip: auto
}

[role=button] {
	cursor: pointer
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
	margin-bottom: .5rem;
	font-family: inherit;
	font-weight: 500;
	line-height: 1.2;
	color: inherit
}

.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
	font-weight: 400;
	line-height: 1.2;
	color: #777
}

.h1,
.h2,
.h3,
h1,
h2,
h3 {
	margin-top: 20px;
	margin-bottom: 5px
}

.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small {
	font-size: 65%
}

.h4,
.h5,
.h6,
h4,
h5,
h6 {
	margin-top: 0;
	margin-bottom: .5rem
}

.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
	font-size: 75%
}

.h1,
h1 {
	font-size: 2.5rem
}

.h2,
h2 {
	font-size: 1.8rem
}

.h3,
h3 {
	font-size: 1.75rem
}

.h4,
h4 {
	font-size: 1.5rem
}

.h5,
h5 {
	font-size: 1.25rem
}

.h6,
h6 {
	font-size: 1rem
}

.box-main p {
	color: #fff
}

p {
	margin: 0 0 10px;
	margin-bottom: 1rem
}

.lead {
	margin-bottom: 20px;
	font-size: 16px;
	font-weight: 300;
	line-height: 1.4
}

@media (min-width:768px) {
	.lead {
		font-size: 21px
	}
}

.small,
small {
	font-size: 85%
}

.mark,
mark {
	padding: .2em;
	background-color: #fcf8e3
}

.text-left {
	text-align: left
}

.text-left p {
	font-size: 14px
}

.text-left h3 {
	font-size: 1.3rem
}

.text-right {
	text-align: right
}

.text-center {
	text-align: center
}

.text-justify {
	text-align: justify
}

.text-nowrap {
	white-space: nowrap
}

.text-lowercase {
	text-transform: lowercase
}

.text-uppercase {
	text-transform: uppercase
}

.text-capitalize {
	text-transform: capitalize
}

.text-muted {
	color: #777
}

.text-primary {
	color: #337ab7
}

a.text-primary:focus,
a.text-primary:hover {
	color: #286090
}

.text-success {
	color: #3c763d
}

a.text-success:focus,
a.text-success:hover {
	color: #2b542c
}

.text-info {
	color: #31708f
}

a.text-info:focus,
a.text-info:hover {
	color: #245269
}

.text-warning {
	color: #8a6d3b
}

a.text-warning:focus,
a.text-warning:hover {
	color: #66512c
}

.text-danger {
	color: #a94442
}

a.text-danger:focus,
a.text-danger:hover {
	color: #843534
}

.bg-primary {
	color: #fff;
	background-color: #337ab7
}

a.bg-primary:focus,
a.bg-primary:hover {
	background-color: #286090
}

.bg-success {
	background-color: #dff0d8
}

a.bg-success:focus,
a.bg-success:hover {
	background-color: #c1e2b3
}

.bg-info {
	background-color: #d9edf7
}

a.bg-info:focus,
a.bg-info:hover {
	background-color: #afd9ee
}

.bg-warning {
	background-color: #fcf8e3
}

a.bg-warning:focus,
a.bg-warning:hover {
	background-color: #f7ecb5
}

.bg-danger {
	background-color: #f2dede
}

a.bg-danger:focus,
a.bg-danger:hover {
	background-color: #e4b9b9
}

.page-header {
	padding-bottom: 9px;
	margin: 40px 0 20px;
	border-bottom: 1px solid #eee
}

ol,
ul {
	margin-top: 0;
	margin-bottom: 10px
}

ol ol,
ol ul,
ul ol,
ul ul {
	margin-bottom: 0
}

.list-unstyled {
	padding-left: 0;
	list-style: none
}

.list-inline {
	padding-left: 0;
	margin-left: -5px;
	list-style: none
}

.list-inline>li {
	display: inline-block;
	padding-right: 5px;
	padding-left: 5px
}

dl {
	margin-top: 0;
	margin-bottom: 20px
}

dd,
dt {
	line-height: 1.42857143
}

dt {
	font-weight: 700
}

dd {
	margin-left: 0
}

@media (min-width:768px) {
	.dl-horizontal dt {
		float: left;
		width: 160px;
		overflow: hidden;
		clear: left;
		text-align: right;
		text-overflow: ellipsis;
		white-space: nowrap
	}
	.dl-horizontal dd {
		margin-left: 180px
	}
}

abbr[data-original-title],
abbr[title] {
	cursor: help;
	border-bottom: 1px dotted #777
}

.initialism {
	font-size: 90%;
	text-transform: uppercase
}

blockquote {
	padding: 10px 20px;
	margin: 0 0 20px;
	font-size: 17.5px;
	border-left: 5px solid #eee
}

blockquote ol:last-child,
blockquote p:last-child,
blockquote ul:last-child {
	margin-bottom: 0
}

blockquote .small,
blockquote footer,
blockquote small {
	display: block;
	font-size: 80%;
	line-height: 1.42857143;
	color: #777
}

blockquote .small:before,
blockquote footer:before,
blockquote small:before {
	content: '\2014 \00A0'
}

.blockquote-reverse,
blockquote.pull-right {
	padding-right: 15px;
	padding-left: 0;
	text-align: right;
	border-right: 5px solid #eee;
	border-left: 0
}

.blockquote-reverse .small:before,
.blockquote-reverse footer:before,
.blockquote-reverse small:before,
blockquote.pull-right .small:before,
blockquote.pull-right footer:before,
blockquote.pull-right small:before {
	content: ''
}

.blockquote-reverse .small:after,
.blockquote-reverse footer:after,
.blockquote-reverse small:after,
blockquote.pull-right .small:after,
blockquote.pull-right footer:after,
blockquote.pull-right small:after {
	content: '\00A0 \2014'
}

address {
	margin-bottom: 20px;
	font-style: normal;
	line-height: 1.42857143
}

code,
kbd,
pre,
samp {
	font-family: Menlo, Monaco, Consolas, "Courier New", monospace
}

code {
	padding: 2px 4px;
	font-size: 90%;
	color: #c7254e;
	background-color: #f9f2f4;
	border-radius: 4px
}

kbd {
	padding: 2px 4px;
	font-size: 90%;
	color: #fff;
	background-color: #333;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25);
	box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25)
}

kbd kbd {
	padding: 0;
	font-size: 100%;
	font-weight: 700;
	-webkit-box-shadow: none;
	box-shadow: none
}

pre {
	display: block;
	padding: 9.5px;
	margin: 0 0 10px;
	font-size: 13px;
	line-height: 1.42857143;
	color: #333;
	word-break: break-all;
	word-wrap: break-word;
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	border-radius: 4px
}

pre code {
	padding: 0;
	font-size: inherit;
	color: inherit;
	white-space: pre-wrap;
	background-color: transparent;
	border-radius: 0
}

.pre-scrollable {
	max-height: 340px;
	overflow-y: scroll
}

#testimonials .container {
	width: 95%
}

.container {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto/*! margin-bottom:-5px; */
}

.container2 {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
	margin-bottom: -5px
}

@media (min-width:768px) {
	.container {
		width: 700px
	}
	.container2 {
		width: 700px
	}
}

@media (min-width:992px) {
	#testimonials .container {
		width: 85%
	}
	.container {
		width: 950px
	}
	.container2 {
		width: 950px
	}
}

@media (max-width:1190px) {
	.bg-animation {
		height: 950px
	}
}

@media (max-width:767px) {
	.bg-animation {
		position: absolute;
		opacity: 1;
		left: 0;
		top: -20%;
		width: 100%;
		height: 770px;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-color: #00ea8a;
		-webkit-transform: skewY(-4deg);
		-moz-transform: skewY(-4deg);
		-ms-transform: skewY(-4deg);
		transform: skewY(-4deg);
		z-index: -1;
		background-color: #130f40;
		background-image: linear-gradient(to left top, #162239, #232546, #362651, #4d2557, #651f57)
	}
}

@media (min-width:1400px) {
	.bg-animation {
		position: absolute;
		opacity: 1;
		left: 0;
		top: -20%;
		width: 100%;
		height: 1040px;
		background-repeat: no-repeat/*! background-size:100%; */
		/*! background-color:#00ea8a; */
		/*! -webkit-transform:skewY(-5deg); */
		/*! -moz-transform:skewY(-5deg); */
		/*! -ms-transform:skewY(-5deg); */
		/*! transform:skewY(-5deg); */
		/*! z-index:-1; */
		/*! background-image: url(/img/header.svg); */
	}
	header .intro-1 {
		padding-top: 200px!important;
		padding-bottom: 350px!important
	}
}

@media (min-width:1200px) {
	.stats .container {
		width: 87%
	}
	.container {
		width: 90%
	}
	.container2 {
		width: 89%
	}
}

.container-fluid {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto
}

.row {
	margin-right: -15px;
	margin-left: -15px
}

table {
	background-color: transparent
}

caption {
	padding-top: 8px;
	padding-bottom: 8px;
	color: #777;
	text-align: left
}

th {
	text-align: left
}

.table {
	width: 100%;
	max-width: 100%;
	margin-bottom: 20px
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
	padding: 12px;
	line-height: 1.42857143;
	vertical-align: top;
	border: 1px solid #ddd/*! font-size:15px */
}

.table>thead>tr>th {
	vertical-align: bottom;
	border-bottom: 1px solid #ddd
}

.table>caption+thead>tr:first-child>td,
.table>caption+thead>tr:first-child>th,
.table>colgroup+thead>tr:first-child>td,
.table>colgroup+thead>tr:first-child>th,
.table>thead:first-child>tr:first-child>td,
.table>thead:first-child>tr:first-child>th {
	border-top: -1px
}

.table>tbody+tbody {
	border-top: 2px solid #ddd
}

.table .table {
	background-color: #fff
}

.table-condensed>tbody>tr>td,
.table-condensed>tbody>tr>th,
.table-condensed>tfoot>tr>td,
.table-condensed>tfoot>tr>th,
.table-condensed>thead>tr>td,
.table-condensed>thead>tr>th {
	padding: 5px
}

.table-bordered {
	border: 1px solid #ddd
}

.table-bordered>tbody>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>thead>tr>th {
	border: 1px solid #ddd
}

.table-bordered>thead>tr>td,
.table-bordered>thead>tr>th {
	border-bottom-width: 2px
}

.table-striped>tbody>tr:nth-of-type(odd) {
	background-color: #121d33
}

.table-hover>tbody>tr:hover {
	background-color: #121d33
}

table col[class*=col-] {
	position: static;
	display: table-column;
	float: none
}

table td[class*=col-],
table th[class*=col-] {
	position: static;
	display: table-cell;
	float: none
}

.table>tbody>tr.active>td,
.table>tbody>tr.active>th,
.table>tbody>tr>td.active,
.table>tbody>tr>th.active,
.table>tfoot>tr.active>td,
.table>tfoot>tr.active>th,
.table>tfoot>tr>td.active,
.table>tfoot>tr>th.active,
.table>thead>tr.active>td,
.table>thead>tr.active>th,
.table>thead>tr>td.active,
.table>thead>tr>th.active {
	background-color: #f5f5f5
}

.table-hover>tbody>tr.active:hover>td,
.table-hover>tbody>tr.active:hover>th,
.table-hover>tbody>tr:hover>.active,
.table-hover>tbody>tr>td.active:hover,
.table-hover>tbody>tr>th.active:hover {
	background-color: #e8e8e8
}

.table>tbody>tr.success>td,
.table>tbody>tr.success>th,
.table>tbody>tr>td.success,
.table>tbody>tr>th.success,
.table>tfoot>tr.success>td,
.table>tfoot>tr.success>th,
.table>tfoot>tr>td.success,
.table>tfoot>tr>th.success,
.table>thead>tr.success>td,
.table>thead>tr.success>th,
.table>thead>tr>td.success,
.table>thead>tr>th.success {
	background-color: #dff0d8
}

.table-hover>tbody>tr.success:hover>td,
.table-hover>tbody>tr.success:hover>th,
.table-hover>tbody>tr:hover>.success,
.table-hover>tbody>tr>td.success:hover,
.table-hover>tbody>tr>th.success:hover {
	background-color: #d0e9c6
}

.table>tbody>tr.info>td,
.table>tbody>tr.info>th,
.table>tbody>tr>td.info,
.table>tbody>tr>th.info,
.table>tfoot>tr.info>td,
.table>tfoot>tr.info>th,
.table>tfoot>tr>td.info,
.table>tfoot>tr>th.info,
.table>thead>tr.info>td,
.table>thead>tr.info>th,
.table>thead>tr>td.info,
.table>thead>tr>th.info {
	background-color: #d9edf7
}

.table-hover>tbody>tr.info:hover>td,
.table-hover>tbody>tr.info:hover>th,
.table-hover>tbody>tr:hover>.info,
.table-hover>tbody>tr>td.info:hover,
.table-hover>tbody>tr>th.info:hover {
	background-color: #c4e3f3
}

.table>tbody>tr.warning>td,
.table>tbody>tr.warning>th,
.table>tbody>tr>td.warning,
.table>tbody>tr>th.warning,
.table>tfoot>tr.warning>td,
.table>tfoot>tr.warning>th,
.table>tfoot>tr>td.warning,
.table>tfoot>tr>th.warning,
.table>thead>tr.warning>td,
.table>thead>tr.warning>th,
.table>thead>tr>td.warning,
.table>thead>tr>th.warning {
	background-color: #fcf8e3
}

.table-hover>tbody>tr.warning:hover>td,
.table-hover>tbody>tr.warning:hover>th,
.table-hover>tbody>tr:hover>.warning,
.table-hover>tbody>tr>td.warning:hover,
.table-hover>tbody>tr>th.warning:hover {
	background-color: #faf2cc
}

.table>tbody>tr.danger>td,
.table>tbody>tr.danger>th,
.table>tbody>tr>td.danger,
.table>tbody>tr>th.danger,
.table>tfoot>tr.danger>td,
.table>tfoot>tr.danger>th,
.table>tfoot>tr>td.danger,
.table>tfoot>tr>th.danger,
.table>thead>tr.danger>td,
.table>thead>tr.danger>th,
.table>thead>tr>td.danger,
.table>thead>tr>th.danger {
	background-color: #f2dede
}

.table-hover>tbody>tr.danger:hover>td,
.table-hover>tbody>tr.danger:hover>th,
.table-hover>tbody>tr:hover>.danger,
.table-hover>tbody>tr>td.danger:hover,
.table-hover>tbody>tr>th.danger:hover {
	background-color: #ebcccc
}

.table-responsive {
	min-height: .01%;
	overflow-x: auto
}

@media screen and (max-width:767px) {
	.table-responsive {
		width: 100%;
		margin-bottom: 15px;
		overflow-y: hidden;
		-ms-overflow-style: -ms-autohiding-scrollbar;
		border: 1px solid #ddd
	}
	.table-responsive>.table {
		margin-bottom: 0
	}
	.table-responsive>.table>tbody>tr>td,
	.table-responsive>.table>tbody>tr>th,
	.table-responsive>.table>tfoot>tr>td,
	.table-responsive>.table>tfoot>tr>th,
	.table-responsive>.table>thead>tr>td,
	.table-responsive>.table>thead>tr>th {
		white-space: nowrap
	}
	.table-responsive>.table-bordered {
		border: 0
	}
	.table-responsive>.table-bordered>tbody>tr>td:first-child,
	.table-responsive>.table-bordered>tbody>tr>th:first-child,
	.table-responsive>.table-bordered>tfoot>tr>td:first-child,
	.table-responsive>.table-bordered>tfoot>tr>th:first-child,
	.table-responsive>.table-bordered>thead>tr>td:first-child,
	.table-responsive>.table-bordered>thead>tr>th:first-child {
		border-left: 0
	}
	.table-responsive>.table-bordered>tbody>tr>td:last-child,
	.table-responsive>.table-bordered>tbody>tr>th:last-child,
	.table-responsive>.table-bordered>tfoot>tr>td:last-child,
	.table-responsive>.table-bordered>tfoot>tr>th:last-child,
	.table-responsive>.table-bordered>thead>tr>td:last-child,
	.table-responsive>.table-bordered>thead>tr>th:last-child {
		border-right: 0
	}
	.table-responsive>.table-bordered>tbody>tr:last-child>td,
	.table-responsive>.table-bordered>tbody>tr:last-child>th,
	.table-responsive>.table-bordered>tfoot>tr:last-child>td,
	.table-responsive>.table-bordered>tfoot>tr:last-child>th {
		border-bottom: 0
	}
}

fieldset {
	min-width: 0;
	padding: 0;
	margin: 0;
	border: 0
}

legend {
	display: block;
	width: 100%;
	padding: 0;
	margin-bottom: 20px;
	font-size: 21px;
	line-height: inherit;
	color: #333;
	border: 0;
	border-bottom: 1px solid #e5e5e5
}

label {
	display: inline-block;
	max-width: 100%;
	margin-bottom: .5rem;
	font-weight: 500;
	color: #fff
}

input[type=search] {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}

input[type=checkbox],
input[type=radio] {
	margin: 4px 5px 0;
	line-height: normal
}

input[type=file] {
	display: block
}

input[type=range] {
	display: block;
	width: 100%
}

select[multiple],
select[size] {
	height: auto
}

input[type=checkbox]:focus,
input[type=file]:focus,
input[type=radio]:focus {
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}

output {
	display: block;
	padding-top: 7px;
	font-size: 14px;
	line-height: 1.42857143;
	color: #555
}

input[type=button].btn-block,
input[type=reset].btn-block,
input[type=submit].btn-block {
	width: 100%
}

.fade {
	opacity: 0;
	-webkit-transition: opacity .15s linear;
	-o-transition: opacity .15s linear;
	transition: opacity .15s linear
}

.fade.in {
	opacity: 1
}

.collapse {
	display: none
}

.collapse.in {
	display: block
}

tr.collapse.in {
	display: table-row
}

tbody.collapse.in {
	display: table-row-group
}

.collapsing {
	position: relative;
	height: 0;
	overflow: hidden;
	-webkit-transition-timing-function: ease;
	-o-transition-timing-function: ease;
	transition-timing-function: ease;
	-webkit-transition-duration: .2s;
	-o-transition-duration: .2s;
	transition-duration: .2s;
	-webkit-transition-property: height, visibility;
	-o-transition-property: height, visibility;
	transition-property: height, visibility
}

.caret {
	display: inline-block;
	width: 0;
	height: 0;
	margin-left: 2px;
	vertical-align: middle;
	border-top: 4px dashed;
	border-right: 4px solid transparent;
	border-left: 4px solid transparent
}

.dropdown,
.dropup {
	position: relative
}

.dropdown-toggle:focus {
	outline: 0
}

.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 1000;
	display: none;
	float: left;
	min-width: 160px;
	padding: 5px 0;
	margin: 2px 0 0;
	font-size: 14px;
	text-align: left;
	list-style: none;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, .15);
	border-radius: 4px;
	-webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
	box-shadow: 0 6px 12px rgba(0, 0, 0, .175)
}

.dropdown-menu.pull-right {
	right: 0;
	left: auto
}

.dropdown-menu .divider {
	height: 1px;
	margin: 9px 0;
	overflow: hidden;
	background-color: #e5e5e5
}

.dropdown-menu>li>a {
	display: block;
	padding: 3px 20px;
	clear: both;
	font-weight: 400;
	line-height: 1.42857143;
	color: #333;
	white-space: nowrap
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
	color: #262626;
	text-decoration: none;
	background-color: #f5f5f5
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:focus,
.dropdown-menu>.active>a:hover {
	color: #fff;
	text-decoration: none;
	background-color: #337ab7;
	outline: 0
}

.dropdown-menu>.disabled>a,
.dropdown-menu>.disabled>a:focus,
.dropdown-menu>.disabled>a:hover {
	color: #777
}

.dropdown-menu>.disabled>a:focus,
.dropdown-menu>.disabled>a:hover {
	text-decoration: none;
	cursor: not-allowed;
	background-color: transparent;
	background-image: none
}

.open>.dropdown-menu {
	display: block
}

.open>a {
	outline: 0
}

.dropdown-menu-right {
	right: 0;
	left: auto
}

.dropdown-menu-left {
	right: auto;
	left: 0
}

.dropdown-header {
	display: block;
	padding: 3px 20px;
	font-size: 12px;
	line-height: 1.42857143;
	color: #777;
	white-space: nowrap
}

.dropdown-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 990
}

.pull-right>.dropdown-menu {
	right: 0;
	left: auto
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
	content: "";
	border-top: 0;
	border-bottom: 4px dashed
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
	top: auto;
	bottom: 100%;
	margin-bottom: 2px
}

@media (min-width:768px) {
	.navbar-right .dropdown-menu {
		right: 0;
		left: auto
	}
	.navbar-right .dropdown-menu-left {
		right: auto;
		left: 0
	}
}

.nav {
	padding-left: 0;
	margin-bottom: 0;
	list-style: none
}

.nav>li {
	position: relative;
	display: block
}

.nav>li>a {
	position: relative;
	display: block;
	padding: 10px 15px
}

.nav>li>a:focus,
.nav>li>a:hover {
	text-decoration: none;
	background-color: #eee
}

.nav>li.disabled>a {
	color: #777
}

.nav>li.disabled>a:focus,
.nav>li.disabled>a:hover {
	color: #777;
	text-decoration: none;
	cursor: not-allowed;
	background-color: transparent
}

.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover {
	background-color: #eee;
	border-color: #337ab7
}

.nav .nav-divider {
	height: 1px;
	margin: 9px 0;
	overflow: hidden;
	background-color: #e5e5e5
}

.nav>li>a>img {
	max-width: none
}

.nav-tabs {
	border-bottom: 1px solid #ddd
}

.nav-tabs>li {
	float: left;
	margin-bottom: -1px
}

.nav-tabs>li>a {
	margin-right: 2px;
	line-height: 1.42857143;
	border: 1px solid transparent;
	border-radius: 4px 4px 0 0
}

.nav-tabs>li>a:hover {
	border-color: #eee #eee #ddd
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover {
	color: #fff;
	cursor: default;
	background-color: #121d33;
	border: 1px solid #ddd;
	border-bottom-color: transparent
}

.nav-tabs.nav-justified {
	width: 100%;
	border-bottom: 0
}

.nav-tabs.nav-justified>li {
	float: none
}

.nav-tabs.nav-justified>li>a {
	margin-bottom: 5px;
	text-align: center
}

.nav-tabs.nav-justified>.dropdown .dropdown-menu {
	top: auto;
	left: auto
}

@media (min-width:768px) {
	.nav-tabs.nav-justified>li {
		display: table-cell;
		width: 1%
	}
	.nav-tabs.nav-justified>li>a {
		margin-bottom: 0
	}
}

.nav-tabs.nav-justified>li>a {
	margin-right: 0;
	border-radius: 4px
}

.nav-tabs.nav-justified>.active>a,
.nav-tabs.nav-justified>.active>a:focus,
.nav-tabs.nav-justified>.active>a:hover {
	border: 1px solid #ddd
}

@media (min-width:768px) {
	.nav-tabs.nav-justified>li>a {
		border-bottom: 1px solid #ddd;
		border-radius: 4px 4px 0 0
	}
	.nav-tabs.nav-justified>.active>a,
	.nav-tabs.nav-justified>.active>a:focus,
	.nav-tabs.nav-justified>.active>a:hover {
		border-bottom-color: #fff
	}
}

.nav-pills>li {
	float: left
}

.nav-pills>li>a {
	border-radius: 4px
}

.nav-pills>li+li {
	margin-left: 2px
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:focus,
.nav-pills>li.active>a:hover {
	color: #fff;
	background-color: #337ab7
}

.nav-stacked>li {
	float: none
}

.nav-stacked>li+li {
	margin-top: 2px;
	margin-left: 0
}

.nav-justified {
	width: 100%
}

.nav-justified>li {
	float: none
}

.nav-justified>li>a {
	margin-bottom: 5px;
	text-align: center
}

.nav-justified>.dropdown .dropdown-menu {
	top: auto;
	left: auto
}

@media (min-width:768px) {
	.nav-justified>li {
		display: table-cell;
		width: 1%
	}
	.nav-justified>li>a {
		margin-bottom: 0
	}
}

.nav-tabs-justified {
	border-bottom: 0
}

.nav-tabs-justified>li>a {
	margin-right: 0;
	border-radius: 4px
}

.nav-tabs-justified>.active>a,
.nav-tabs-justified>.active>a:focus,
.nav-tabs-justified>.active>a:hover {
	border: 1px solid #ddd
}

@media (min-width:768px) {
	.nav-tabs-justified>li>a {
		border-bottom: 1px solid #ddd;
		border-radius: 4px 4px 0 0
	}
	.nav-tabs-justified>.active>a,
	.nav-tabs-justified>.active>a:focus,
	.nav-tabs-justified>.active>a:hover {
		border-bottom-color: #fff
	}
}

.tab-content>.tab-pane {
	display: none
}

.tab-content>.active {
	display: block
}

.nav-tabs .dropdown-menu {
	margin-top: -1px;
	border-top-left-radius: 0;
	border-top-right-radius: 0
}

.navbar {
	position: relative;
	min-height: 50px;
	margin-bottom: 20px
}

@media (min-width:768px) {
	.menu-h {
		float: left
	}
}

.menu-fi {
	padding-right: 15px;
	padding-left: 15px;
	overflow-x: visible;
	-webkit-overflow-scrolling: touch
}

.menu-fi.in {
	overflow-y: auto
}

@media (min-width:768px) {
	.menu-fi {
		width: auto;
		border-top: 0;
		-webkit-box-shadow: none;
		box-shadow: none
	}
	.menu-fi.collapse {
		display: block!important;
		height: auto!important;
		padding-bottom: 0;
		overflow: visible!important
	}
	.menu-fi.in {
		overflow-y: visible
	}
	.navbar-fixed-bottom .menu-fi,
	.navbar-fixed-top .menu-fi,
	.navbar-static-top .menu-fi {
		padding-right: 0;
		padding-left: 0
	}
}

.navbar-fixed-bottom .menu-fi,
.navbar-fixed-top .menu-fi {
	max-height: 340px
}

@media (max-device-width:480px) and (orientation:landscape) {
	.navbar-fixed-bottom .menu-fi,
	.navbar-fixed-top .menu-fi {
		max-height: 200px
	}
}

.container-fluid>.menu-fi,
.container-fluid>.menu-h,
.container>.menu-fi,
.container>.menu-h {
	margin-right: -15px;
	margin-left: -15px
}

@media (min-width:768px) {
	.container-fluid>.menu-fi,
	.container-fluid>.menu-h,
	.container>.menu-fi,
	.container>.menu-h {
		margin-right: 0;
		margin-left: 0
	}
}

.navbar-static-top {
	z-index: 1000;
	border-width: 0 0 1px
}

@media (min-width:768px) {
	.navbar-static-top {
		border-radius: 0
	}
}

.navbar-fixed-bottom,
.navbar-fixed-top {
	position: fixed;
	right: 0;
	left: 0;
	z-index: 1030
}

@media (min-width:768px) {
	.navbar-fixed-bottom,
	.navbar-fixed-top {
		border-radius: 0
	}
}

.navbar-fixed-top {
	top: 0;
	border-width: 0 0 1px;
	transition: all 0 ease-in-out 0
}

.navbar-fixed-bottom {
	bottom: 0;
	margin-bottom: 0;
	border-width: 1px 0 0
}

.navbar-brand {
	float: center;
	height: 50px;
	padding: 20px;
	font-size: 18px;
	line-height: 10px
}

.navbar-brand:focus,
.navbar-brand:hover {
	text-decoration: none
}

.navbar-brand>img {
	display: block
}

@media (min-width:768px) {
	.navbar>.container .navbar-brand,
	.navbar>.container-fluid .navbar-brand {
		margin-left: -60px
	}
	.navbar-brand {
		line-height: 40px
	}
}

.s-t {
	position: relative;
	float: right;
	padding: 9px 10px;
	margin-top: 8px;
	margin-right: 5px;
	margin-bottom: 8px;
	background-image: none;
	border: 1px;
	z-index: 1
}

.s-t:focus {
	outline: 0
}

.s-t .nv {
	display: block;
	width: 22px;
	height: 2px;
	border-radius: 1px
}

.s-t .nv+.nv {
	margin-top: 4px
}

@media (min-width:768px) {
	.s-t {
		display: none
	}
}

.navbar-nav>li>a {
	padding-top: 10px;
	padding-bottom: 10px;
	line-height: 20px
}

@media (max-width:767px) {
	.navbar-nav .open .dropdown-menu {
		position: static;
		float: none;
		width: auto;
		margin-top: 0;
		background-color: transparent;
		border: 0;
		-webkit-box-shadow: none;
		box-shadow: none
	}
	.navbar-nav .open .dropdown-menu .dropdown-header,
	.navbar-nav .open .dropdown-menu>li>a {
		padding: 5px 15px 5px 25px
	}
	.navbar-nav .open .dropdown-menu>li>a {
		line-height: 20px
	}
	.navbar-nav .open .dropdown-menu>li>a:focus,
	.navbar-nav .open .dropdown-menu>li>a:hover {
		background-image: none
	}
}

@media (min-width:768px) {
	.navbar-nav {
		float: left;
		margin: 0
	}
	.navbar-nav>li {
		float: left
	}
	.navbar-nav>li>a {
		padding-top: 15px;
		padding-bottom: 15px
	}
}

.navbar-form {
	padding: 10px 15px;
	border-top: 1px solid transparent;
	border-bottom: 1px solid transparent;
	-webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);
	box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);
	margin: 8px -15px
}

@media (min-width:768px) {
	.navbar-form .form-group {
		display: inline-block;
		margin-bottom: 0;
		vertical-align: middle
	}
	.navbar-form .form-control {
		display: inline-block;
		width: auto;
		vertical-align: middle
	}
	.navbar-form .form-control-static {
		display: inline-block
	}
	.navbar-form .input-group {
		display: inline-table;
		vertical-align: middle
	}
	.navbar-form .input-group .form-control,
	.navbar-form .input-group .input-group-addon,
	.navbar-form .input-group .input-group-btn {
		width: auto
	}
	.navbar-form .input-group>.form-control {
		width: 100%
	}
	.navbar-form .control-label {
		margin-bottom: 0;
		vertical-align: middle
	}
	.navbar-form .checkbox,
	.navbar-form .radio {
		display: inline-block;
		margin-top: 0;
		margin-bottom: 0;
		vertical-align: middle
	}
	.navbar-form .checkbox label,
	.navbar-form .radio label {
		padding-left: 0
	}
	.navbar-form .checkbox input[type=checkbox],
	.navbar-form .radio input[type=radio] {
		position: relative;
		margin-left: 0
	}
	.navbar-form .has-feedback .form-control-feedback {
		top: 0
	}
}

@media (max-width:767px) {
	.navbar-form .form-group {
		margin-bottom: 5px
	}
	.navbar-form .form-group:last-child {
		margin-bottom: 0
	}
}

@media (min-width:768px) {
	.navbar-form {
		width: auto;
		padding-top: 0;
		padding-bottom: 0;
		margin-right: 0;
		margin-left: 0;
		border: 0;
		-webkit-box-shadow: none;
		box-shadow: none
	}
}

.navbar-nav>li>.dropdown-menu {
	margin-top: 0;
	border-top-left-radius: 0;
	border-top-right-radius: 0
}

.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
	margin-bottom: 0;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}

.navbar-btn {
	margin-top: 8px;
	margin-bottom: 8px
}

.navbar-btn.btn-sm {
	margin-top: 10px;
	margin-bottom: 10px
}

.navbar-btn.btn-xs {
	margin-top: 14px;
	margin-bottom: 14px
}

.navbar-text {
	margin-top: 15px;
	margin-bottom: 15px
}

@media (min-width:768px) {
	.navbar-text {
		float: left;
		margin-right: 15px;
		margin-left: 15px
	}
}

@media (min-width:768px) {
	.navbar-left {
		float: left!important
	}
	.navbar-right {
		float: right!important;
		margin-right: -15px
	}
	.navbar-right~.navbar-right {
		margin-right: 0
	}
}

.navbar-default {
	background-color: transparent
}

.navbar-default .navbar-brand {
	color: #fff
}

.navbar-default .navbar-brand:focus,
.navbar-default .navbar-brand:hover {
	color: #5e5e5e;
	background-color: transparent
}

.navbar-default .navbar-text {
	color: #777
}

.navbar-default .navbar-nav>li>a {
	color: #777
}

.navbar-default .navbar-nav>li>a:focus,
.navbar-default .navbar-nav>li>a:hover {
	color: #333;
	background-color: transparent
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.active>a:focus,
.navbar-default .navbar-nav>.active>a:hover {
	color: #555;
	background-color: #e7e7e7
}

.navbar-default .navbar-nav>.disabled>a,
.navbar-default .navbar-nav>.disabled>a:focus,
.navbar-default .navbar-nav>.disabled>a:hover {
	color: #ccc;
	background-color: transparent
}

.navbar-default .s-t {
	border-color: #ddd
}

.navbar-default .s-t .nv {
	background-color: #888
}

.navbar-default .menu-fi,
.navbar-default .navbar-form {
	border-color: #e7e7e7
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:focus,
.navbar-default .navbar-nav>.open>a:hover {
	color: #555;
	background-color: #e7e7e7
}

@media (max-width:767px) {
	.navbar-default .navbar-nav .open .dropdown-menu>li>a {
		color: #777
	}
	.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus,
	.navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
		color: #333;
		background-color: transparent
	}
	.navbar-default .navbar-nav .open .dropdown-menu>.active>a,
	.navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus,
	.navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover {
		color: #555;
		background-color: #e7e7e7
	}
	.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,
	.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus,
	.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover {
		color: #ccc;
		background-color: transparent
	}
}

.navbar-default .navbar-link {
	color: #777
}

.navbar-default .navbar-link:hover {
	color: #333
}

.navbar-default .btn-link {
	color: #777
}

.navbar-default .btn-link:focus,
.navbar-default .btn-link:hover {
	color: #333
}

.navbar-default .btn-link[disabled]:focus,
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:focus,
fieldset[disabled] .navbar-default .btn-link:hover {
	color: #ccc
}

.navbar-inverse {
	background-color: #222;
	border-color: #080808
}

.navbar-inverse .navbar-brand {
	color: #9d9d9d
}

.navbar-inverse .navbar-brand:focus,
.navbar-inverse .navbar-brand:hover {
	color: #fff;
	background-color: transparent
}

.navbar-inverse .navbar-text {
	color: #9d9d9d
}

.navbar-inverse .navbar-nav>li>a {
	color: #9d9d9d
}

.navbar-inverse .navbar-nav>li>a:focus,
.navbar-inverse .navbar-nav>li>a:hover {
	color: #fff;
	background-color: transparent
}

.navbar-inverse .navbar-nav>.active>a,
.navbar-inverse .navbar-nav>.active>a:focus,
.navbar-inverse .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: #080808
}

.navbar-inverse .navbar-nav>.disabled>a,
.navbar-inverse .navbar-nav>.disabled>a:focus,
.navbar-inverse .navbar-nav>.disabled>a:hover {
	color: #444;
	background-color: transparent
}

.navbar-inverse .s-t {
	border-color: #333
}

.navbar-inverse .s-t:focus,
.navbar-inverse .s-t:hover {
	background-color: #333
}

.navbar-inverse .s-t .nv {
	background-color: #fff
}

.navbar-inverse .menu-fi,
.navbar-inverse .navbar-form {
	border-color: #101010
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:focus,
.navbar-inverse .navbar-nav>.open>a:hover {
	color: #fff;
	background-color: #080808
}

@media (max-width:767px) {
	.navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
		border-color: #080808
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu .divider {
		background-color: #080808
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
		color: #9d9d9d
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus,
	.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover {
		color: #fff;
		background-color: transparent
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,
	.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus,
	.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover {
		color: #fff;
		background-color: #080808
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,
	.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus,
	.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover {
		color: #444;
		background-color: transparent
	}
}

.navbar-inverse .navbar-link {
	color: #9d9d9d
}

.navbar-inverse .navbar-link:hover {
	color: #fff
}

.navbar-inverse .btn-link {
	color: #9d9d9d
}

.navbar-inverse .btn-link:focus,
.navbar-inverse .btn-link:hover {
	color: #fff
}

.navbar-inverse .btn-link[disabled]:focus,
.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:focus,
fieldset[disabled] .navbar-inverse .btn-link:hover {
	color: #444
}

.breadcrumb {
	padding: 8px 15px;
	margin-bottom: 20px;
	list-style: none;
	background-color: #f5f5f5;
	border-radius: 4px
}

.breadcrumb>li {
	display: inline-block
}

.breadcrumb>li+li:before {
	padding: 0 5px;
	color: #ccc;
	content: "/\00a0"
}

.breadcrumb>.active {
	color: #777
}

.pagination {
	display: inline-block;
	padding-left: 0;
	margin: 20px 0;
	border-radius: 4px
}

.pagination>li {
	display: inline
}

.pagination>li>a,
.pagination>li>span {
	position: relative;
	float: left;
	padding: 6px 12px;
	margin-left: -1px;
	line-height: 1.42857143;
	color: #337ab7;
	text-decoration: none;
	background-color: #fff;
	border: 1px solid #ddd
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
	margin-left: 0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
	z-index: 2;
	color: #23527c;
	background-color: #eee;
	border-color: #ddd
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
	z-index: 3;
	color: #fff;
	cursor: default;
	background-color: #337ab7;
	border-color: #337ab7
}

.pagination>.disabled>a,
.pagination>.disabled>a:focus,
.pagination>.disabled>a:hover,
.pagination>.disabled>span,
.pagination>.disabled>span:focus,
.pagination>.disabled>span:hover {
	color: #777;
	cursor: not-allowed;
	background-color: #fff;
	border-color: #ddd
}

.pagination-lg>li>a,
.pagination-lg>li>span {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333
}

.pagination-lg>li:first-child>a,
.pagination-lg>li:first-child>span {
	border-top-left-radius: 6px;
	border-bottom-left-radius: 6px
}

.pagination-lg>li:last-child>a,
.pagination-lg>li:last-child>span {
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px
}

.pagination-sm>li>a,
.pagination-sm>li>span {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5
}

.pagination-sm>li:first-child>a,
.pagination-sm>li:first-child>span {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px
}

.pagination-sm>li:last-child>a,
.pagination-sm>li:last-child>span {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px
}

.pager {
	padding-left: 0;
	margin: 20px 0;
	text-align: center;
	list-style: none
}

.pager li {
	display: inline
}

.pager li>a,
.pager li>span {
	display: inline-block;
	padding: 5px 14px;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 15px
}

.pager li>a:focus,
.pager li>a:hover {
	text-decoration: none;
	background-color: #eee
}

.pager .next>a,
.pager .next>span {
	float: right
}

.pager .previous>a,
.pager .previous>span {
	float: left
}

.pager .disabled>a,
.pager .disabled>a:focus,
.pager .disabled>a:hover,
.pager .disabled>span {
	color: #777;
	cursor: not-allowed;
	background-color: #fff
}

.message-area {
	padding-top: 60px;
	padding-bottom: 60px;
	position: relative
}

#form-section>.row {
	position: relative
}

@media(max-width:768px) {
	.dim .tools {
		padding: 30px 15px
	}
	.dim .tools .flex .list {
		width: 100%;
		margin: 15px 0
	}
}

.message-area .bg-color {
	background-color: #f6f9fc;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	-webkit-transform: skewY(-10deg) scaleY(2.5);
	-moz-transform: skewY(-10deg) scaleY(2.5);
	-ms-transform: skewY(-10deg) scaleY(2.5);
	transform: skewY(-10deg) scaleY(2.5);
	-webkit-transform-origin: center center;
	-moz-transform-origin: center center;
	-ms-transform-origin: center center;
	transform-origin: center center;
	z-index: -1
}

.container-fluid {
	width: 100%;
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto
}

.top {
	position: relative;
	display: block;
	text-align: right
}

.top h2 {
	display: block;
	margin: 15px 0;
	color: #667684;
	font-size: 2rem
}

.top h2 strong {
	display: block;
	color: #667684
}

.label {
	display: inline;
	padding: .2em .6em .3em;
	font-size: 75%;
	font-weight: 700;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: .25em
}

a.label:focus,
a.label:hover {
	color: #fff;
	text-decoration: none;
	cursor: pointer
}

.label:empty {
	display: none
}

.btn .label {
	position: relative;
	top: -1px
}

.label-default {
	background-color: #777
}

.label-default[href]:focus,
.label-default[href]:hover {
	background-color: #5e5e5e
}

.label-primary {
	background-color: #337ab7
}

.label-primary[href]:focus,
.label-primary[href]:hover {
	background-color: #286090
}

.label-success {
	background-color: #5cb85c
}

.label-success[href]:focus,
.label-success[href]:hover {
	background-color: #449d44
}

.label-info {
	background-color: #5bc0de
}

.label-info[href]:focus,
.label-info[href]:hover {
	background-color: #31b0d5
}

.label-warning {
	background-color: #f0ad4e
}

.label-warning[href]:focus,
.label-warning[href]:hover {
	background-color: #ec971f
}

.label-danger {
	background-color: #d9534f
}

.label-danger[href]:focus,
.label-danger[href]:hover {
	background-color: #c9302c
}

.badge {
	display: inline-block;
	min-width: 10px;
	padding: 3px 7px;
	font-size: 12px;
	font-weight: 700;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	background-color: #777;
	border-radius: 10px
}

.badge:empty {
	display: none
}

.btn .badge {
	position: relative;
	top: -1px
}

.btn-group-xs>.btn .badge,
.btn-xs .badge {
	top: 0;
	padding: 1px 5px
}

a.badge:focus,
a.badge:hover {
	color: #fff;
	text-decoration: none;
	cursor: pointer
}

.list-group-item.active>.badge,
.nav-pills>.active>a>.badge {
	color: #337ab7;
	background-color: #fff
}

.list-group-item>.badge {
	float: right
}

.list-group-item>.badge+.badge {
	margin-right: 5px
}

.nav-pills>li>a>.badge {
	margin-left: 3px
}

.jumbotron {
	padding-top: 30px;
	padding-bottom: 30px;
	margin-bottom: 30px;
	color: inherit;
	background-color: #eee
}

.jumbotron .h1,
.jumbotron h1 {
	color: inherit
}

.jumbotron p {
	margin-bottom: 15px;
	font-size: 21px;
	font-weight: 200
}

.jumbotron>hr {
	border-top-color: #d5d5d5
}

.container .jumbotron,
.container-fluid .jumbotron {
	padding-right: 15px;
	padding-left: 15px;
	border-radius: 6px
}

.jumbotron .container {
	max-width: 100%
}

@media screen and (min-width:768px) {
	.jumbotron {
		padding-top: 48px;
		padding-bottom: 48px
	}
	.container .jumbotron,
	.container-fluid .jumbotron {
		padding-right: 60px;
		padding-left: 60px
	}
	.jumbotron .h1,
	.jumbotron h1 {
		font-size: 63px
	}
}

.thumbnail {
	display: block;
	padding: 4px;
	margin-bottom: 20px;
	line-height: 1.42857143;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	-webkit-transition: border .2s ease-in-out;
	-o-transition: border .2s ease-in-out;
	transition: border .2s ease-in-out
}

.thumbnail a>img,
.thumbnail>img {
	margin-right: auto;
	margin-left: auto
}

a.thumbnail.active,
a.thumbnail:focus,
a.thumbnail:hover {
	border-color: #337ab7
}

.thumbnail .caption {
	padding: 9px;
	color: #333
}

.btn-group-vertical>.btn-group:after,
.btn-group-vertical>.btn-group:before,
.btn-toolbar:after,
.btn-toolbar:before,
.clearfix:after,
.clearfix:before,
.container-fluid:after,
.container-fluid:before,
.container:after,
.container:before,
.dl-horizontal dd:after,
.dl-horizontal dd:before,
.form-horizontal .form-group:after,
.form-horizontal .form-group:before,
.menu-fi:after,
.menu-fi:before,
.menu-h:after,
.menu-h:before,
.modal-footer:after,
.modal-footer:before,
.modal-header:after,
.modal-header:before,
.nav:after,
.nav:before,
.navbar:after,
.navbar:before,
.pager:after,
.pager:before,
.panel-body:after,
.panel-body:before,
.row:after,
.row:before {
	display: table;
	content: " "
}

.btn-group-vertical>.btn-group:after,
.btn-toolbar:after,
.clearfix:after,
.container-fluid:after,
.container:after,
.dl-horizontal dd:after,
.form-horizontal .form-group:after,
.menu-fi:after,
.menu-h:after,
.modal-footer:after,
.modal-header:after,
.nav:after,
.navbar:after,
.pager:after,
.panel-body:after,
.row:after {
	clear: both
}

.center-block {
	display: block;
	margin-right: auto;
	margin-left: auto
}

.pull-right {
	float: right!important
}

.pull-left {
	float: left!important
}

.hide {
	display: none!important
}

.show {
	display: block!important
}

.invisible {
	visibility: hidden
}

.text-hide {
	font: 0/0 a;
	color: transparent;
	text-shadow: none;
	background-color: transparent;
	border: 0
}

.hidden {
	display: none!important
}

.affix {
	position: fixed;
	background-color: #162239;
	box-shadow: 0 4px 10px rgba(0, 0, 0, .08)
}

.visible-lg,
.visible-md,
.visible-sm,
.visible-xs {
	display: none!important
}

.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block {
	display: none!important
}

@media (max-width:767px) {
	.visible-xs {
		display: block!important
	}
	table.visible-xs {
		display: table!important
	}
	tr.visible-xs {
		display: table-row!important
	}
	td.visible-xs,
	th.visible-xs {
		display: table-cell!important
	}
}

@media (max-width:767px) {
	.visible-xs-block {
		display: block!important
	}
}

@media (max-width:767px) {
	.visible-xs-inline {
		display: inline!important
	}
}

@media (max-width:767px) {
	.visible-xs-inline-block {
		display: inline-block!important
	}
}

@media (min-width:768px) and (max-width:991px) {
	.visible-sm {
		display: block!important
	}
	table.visible-sm {
		display: table!important
	}
	tr.visible-sm {
		display: table-row!important
	}
	td.visible-sm,
	th.visible-sm {
		display: table-cell!important
	}
	.visible-sm-block {
		display: block!important
	}
	.visible-sm-inline {
		display: inline!important
	}
	.visible-sm-inline-block {
		display: inline-block!important
	}
}

@media (min-width:992px) and (max-width:1199px) {
	.visible-md {
		display: block!important
	}
	table.visible-md {
		display: table!important
	}
	tr.visible-md {
		display: table-row!important
	}
	td.visible-md,
	th.visible-md {
		display: table-cell!important
	}
	.visible-md-block {
		display: block!important
	}
	.visible-md-inline {
		display: inline!important
	}
	.visible-md-inline-block {
		display: inline-block!important
	}
}

@media (min-width:1200px) {
	.visible-lg {
		display: block!important
	}
	table.visible-lg {
		display: table!important
	}
	tr.visible-lg {
		display: table-row!important
	}
	td.visible-lg,
	th.visible-lg {
		display: table-cell!important
	}
}

@media (min-width:1200px) {
	.visible-lg-block {
		display: block!important
	}
}

@media (min-width:1200px) {
	.visible-lg-inline {
		display: inline!important
	}
}

@media (min-width:1200px) {
	.visible-lg-inline-block {
		display: inline-block!important
	}
}

@media (max-width:767px) {
	.hidden-xs {
		display: none!important
	}
}

@media (min-width:768px) and (max-width:991px) {
	.hidden-sm {
		display: none!important
	}
}

@media (min-width:992px) and (max-width:1199px) {
	.hidden-md {
		display: none!important
	}
}

@media (min-width:1200px) {
	.hidden-lg {
		display: none!important
	}
}

.visible-print {
	display: none!important
}

@media print {
	.visible-print {
		display: block!important
	}
	table.visible-print {
		display: table!important
	}
	tr.visible-print {
		display: table-row!important
	}
	td.visible-print,
	th.visible-print {
		display: table-cell!important
	}
}

.visible-print-block {
	display: none!important
}

@media print {
	.visible-print-block {
		display: block!important
	}
}

.visible-print-inline {
	display: none!important
}

@media print {
	.visible-print-inline {
		display: inline!important
	}
}

.visible-print-inline-block {
	display: none!important
}

@media print {
	.visible-print-inline-block {
		display: inline-block!important
	}
}

@media print {
	.hidden-print {
		display: none!important
	}
}

a,
a.active,
a:active,
a:focus,
a:hover {
	outline: 0;
	text-decoration: none
}

h3 {
	text-transform: none;
	font-weight: 600;
	color: #253145
}

img {
	max-width: 100%
}

.loader,
.loader:after,
.loader:before {
	border-radius: 50%;
	width: 2.5em;
	height: 2.5em;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation: load7 1.8s infinite ease-in-out;
	animation: load7 1.8s infinite ease-in-out
}

.loader {
	font-size: 10px;
	margin: 0 auto;
	position: relative;
	text-indent: -9999em;
	-webkit-transform: translateZ(0);
	-ms-transform: translateZ(0);
	transform: translateZ(0);
	-webkit-animation-delay: -.16s;
	animation-delay: -.16s
}

.loader:before {
	left: -3.5em;
	-webkit-animation-delay: -.32s;
	animation-delay: -.32s
}

.loader:after {
	left: 3.5em
}

.loader:after,
.loader:before {
	content: '';
	position: absolute;
	top: 0
}

@-webkit-keyframes load7 {
	0%,
	100%,
	80% {
		box-shadow: 0 2.5em 0 -1.3em #fff
	}
	40% {
		box-shadow: 0 2.5em 0 0 #fff
	}
}

@keyframes load7 {
	0%,
	100%,
	80% {
		box-shadow: 0 2.5em 0 -1.3em #fff
	}
	40% {
		box-shadow: 0 2.5em 0 0 #fff
	}
}

.div-table {
	display: table
}

.div-tr {
	display: table-row
}

.div-td {
	display: table-cell
}

@media (min-width:768px) {
	.is-table-row {
		display: table;
		width: 100%
	}
	.is-table-row[class*=col-] {
		float: none;
		display: table-cell;
		vertical-align: middle
	}
}

@media (max-width:992px) {
	[class*=col-md-] {
		margin-bottom: 15px
	}
}

@media (max-width:767px) {
	[class*=col-sm-] {
		margin-bottom: 15px
	}
}

section {
	padding: 30px 0;
	background-color: #121d33
}

@media (max-width:767px) {
	.navbar-default .menu-fi {
		background-color: #151621;
		/*! margin-left:-50px; */
		/*! margin-right:-20px; */
		transform: translateX(-300px);
		max-width: 250px;
		min-height: 1200px;
		margin-top: -56px;
		position: fixed;
		-webkit-transition: all .15s ease-out;
		transition: all .15s ease-out;
		/*! float: right; */
		padding-top: 15px;
		box-shadow: 0 0 20px rgba(0, 0, 0, .3)
	}
	.navbar-default .navbar-nav>li>a {
		text-transform: capitalize
	}
	.navbar-default .menu-fi.apse {
		transform: translateX(-5px);
		-webkit-transition: all .15s ease-in;
		transition: all .15s ease-in
	}
	.navbar-default .navbar-nav>li>a {
		border-bottom: 1px solid rgba(29, 31, 45, .62)
	}
	.fi-img {
		display: block!important;
		text-align: center;
		padding: 80px 0
	}
	.fi-img img {
		width: 238px
	}
}

.inner-page section {
	padding: 20px 0
}

.bg-light-gray {
	background-color: #f6fafe;
	color: #616161
}

.bg-darkest-gray {
	background-color: #222
}

.navbar-default {
	text-transform: uppercase;
	padding: 6px 5px
}

.navbar-default.shrink {
	background-color: #00ea8a
}

.navbar-default .navbar-nav {
	font-size: 13px
}

.navbar-brand {
	height: 48px/*! padding: 5px !important; */
}

.navbar-default .navbar-brand.active,
.navbar-default .navbar-brand:active,
.navbar-default .navbar-brand:focus,
.navbar-default .navbar-brand:hover {
	color: #fff
}

.navbar-brand.logo-image {
	padding: 2px 10px
}

.navbar-brand.logo-image img {
	height: 100%
}

.navbar-default .menu-fi {
	border-color: rgba(255, 255, 255, .02)
}

.navbar-default .s-t {
	background-color: transparent
}

.navbar-default .s-t .nv {
	background-color: rgba(255, 255, 255, .91)
}

.navbar-default .navbar-nav>li>a {
	font-weight: 400;
	/*! letter-spacing:1px; */
	color: #fff;
	padding: .7rem .9rem;
	font-weight: 500;
	color: #fff;
	font-size: 13px;
	line-height: 24px;
	position: relative
}

.navbar-default .nav li a:focus,
.navbar-default .nav li a:hover {
	color: #fff;
	outline: 0
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:focus,
.navbar-default .navbar-nav>.open>a:hover {
	color: #dedee8;
	background-color: transparent
}

.navbar-default .navbar-nav>.active>a {
	border-radius: 0;
	color: #fff;
	background-color: #fed136
}

.navbar-default .navbar-nav>.active>a:focus,
.navbar-default .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: #fec503
}

.dropdown-menu {
	padding: 0;
	min-width: 175px
}

.dropdown-menu>li>a {
	font-size: 11px;
	padding: 8px 10px;
	color: #505050;
	border-bottom: 1px solid #dfdfdf
}

.dropdown-menu li:last-child a {
	border-bottom: none
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
	color: #fff;
	background-color: #1e70dc
}

@media (min-width:768px) {
	.navbar-default {
		padding: 2px;
		-webkit-transition: padding .3s;
		-moz-transition: padding .3s;
		transition: padding .3s;
		border: none
	}
	.captcha-page .navbar-default {
		padding: 0
	}
	.navbar-brand {
		height: 64px
	}
	.navbar-default .navbar-brand {
		font-size: 1.25rem;
		-webkit-transition: all .3s;
		-moz-transition: all .3s;
		transition: all .3s;
		padding: 10px
	}
	.navbar-default .navbar-nav>.active>a {
		border-radius: 3px
	}
	.navbar-default.affix {
		padding: 2px;
		box-shadow: 0 4px 10px rgba(0, 0, 0, .08)
	}
	.navbar-default.affix .navbar-brand {
		font-size: 1.25rem;
		/*! height:60px; */
		/*! margin: 3px 0px; */
		padding: 10px
	}
	.navbar-default .navbar-nav>li>a {
		padding: 20px 10px
	}
	.navbar-default.affix .navbar-nav>li>a {
		padding: 20px 10px
	}
	.navbar-default .navbar-nav>li.language-selector>a,
	.navbar-default.affix .navbar-nav>li.language-selector>a {
		font-size: 21px
	}
}

@media (min-width:768px) and (max-width:992px) {
	.navbar-default .navbar-nav>li>a,
	.navbar-default.affix .navbar-nav>li>a {
		padding-left: 5px;
		padding-right: 5px
	}
}

@media (max-width:767px) {
	.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus,
	.navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
		color: #dedee8;
		outline: 0
	}
	.navbar-default .navbar-nav .open .dropdown-menu>li>a {
		color: #fff
	}
}

.interstitial-page #mainNav,
header {
	background-color: #3a435c;
	margin-top: -2px;
	margin-bottom: -2px;
	color: #fff;
	background-size: cover;
	padding-top: 80px;
	padding-bottom: 0/*! background-size:400% 400%; */
	/*! -webkit-animation:AnimationName 31s ease infinite; */
	/*! -moz-animation:AnimationName 31s ease infinite; */
	/*! animation:AnimationName 31s ease infinite */
}

@-webkit-keyframes AnimationName {
	0% {
		background-position: 97% 0
	}
	50% {
		background-position: 4% 100%
	}
	100% {
		background-position: 97% 0
	}
}

@-moz-keyframes AnimationName {
	0% {
		background-position: 97% 0
	}
	50% {
		background-position: 4% 100%
	}
	100% {
		background-position: 97% 0
	}
}

@keyframes AnimationName {
	0% {
		background-position: 97% 0
	}
	50% {
		background-position: 4% 100%
	}
	100% {
		background-position: 97% 0
	}
}

.shorten {
	background-color: transparent/*! background-repeat:no-repeat; */
	/*! background-color:#130f40; */
	/*! background-image:linear-gradient(to left top,#162239,#232546,#362651,#4d2557,#651f57) */
}

@media (min-width:768px) {
	.interstitial-page #mainNav,
	header {
		padding-top: 0;
		padding-bottom: 0
	}
}

@media (min-width:375px) and (max-width:550px) {
	.interstitial-page #mainNav,
	header {
		/*! padding-top:130px; */
		/*! padding-bottom:28% */
	}
	header .intro-1 {
		/*! padding-top:10px!important; */
		/*! padding-bottom:170px!important */
	}
	.bg-animation {
		/*! height:95%; */
	}
}

.banner-page #mainNav,
.captcha-page #mainNav {
	background-color: #162239;
	color: #fff;
	z-index: 1
}

header .intro-1 {
	text-align: center;
	padding-top: 145px;
	padding-bottom: 220px
}

header .intro-text .intro-lead-in {
	font-weight: 700;
	text-transform: uppercase;
	font-size: 22px;
	line-height: 22px;
	margin-bottom: 25px;
	text-align: center
}

header .intro-text .intro-heading {
	font-weight: 700;
	text-transform: uppercase;
	font-weight: 700;
	font-size: 50px;
	line-height: 50px;
	margin-bottom: 25px
}

@media (max-width:767px) {
	section.reviews {
		top: -2px!important
	}
	.shorten #shorten input.input-lg {
		display: inline-block;
		width: 92%;
		vertical-align: middle
	}
	.shorten #shorten button {
		width: 50.78px!important;
		padding: 0!important;
		margin-right: 14px
	}
	.shorten #shorten button span {
		display: none
	}
	.shorten #shorten button i {
		margin: 0!important
	}
	header .intro-1 {
		padding-top: 20px;
		padding-bottom: 200px
	}
	header .intro-text .intro-lead-in {
		font-size: 17px;
		line-height: 17px;
		margin: 50px 0 100px
	}
	header .intro-text .intro-heading {
		font-size: 37px;
		line-height: 37px;
		margin-bottom: 20px
	}
}

.shorten #shorten input.input-lg {
	background-color: #fff;
	color: #7085a2;
	border: none;
	border-radius: 38px;
	height: 60px;
	padding-left: 25px;
	padding-right: 60px;
	padding: 2rem
}

.shorten #shorten .form-control::-webkit-input-placeholder {
	color: #7085a2
}

.shorten #shorten .form-control:-moz-placeholder {
	color: #7085a2
}

.shorten #shorten .form-control::-moz-placeholder {
	color: #7085a2
}

.shorten #shorten .form-control:-ms-input-placeholder {
	color: #7085a2
}

.shorten #shorten button i {
	position: relative;
	top: 5px;
	font-size: 25px;
	margin-left: 7px
}

.shorten #shorten button {
	padding: 0 25px;
	font-size: 15px;
	text-transform: uppercase;
	position: absolute;
	right: 7px;
	top: 7px;
	bottom: 7px;
	border-radius: 26px;
	z-index: 1000;
	font-weight: 600;
	line-height: 45px;
	width: 164.41px;
	background: #7ace4c;
	/*! border-color: #426c2a; */
	color: rgba(255, 255, 255, .7);
	border: none
}

.shorten #shorten button:focus {
	outline: 0
}

@media (min-width:768px) {
	header .intro-text {
		padding-top: 260px;
		padding-bottom: 260px
	}
	header .intro-text .intro-lead-in {
		font-size: 23px;
		line-height: 23px;
		margin-bottom: 25px
	}
	.inner-page header .intro-text {
		padding-top: 165px;
		padding-bottom: 100px
	}
	.inner-page header .intro-lead-in {
		font-size: 43px;
		line-height: 43px;
		margin-bottom: 0
	}
	header .intro-text .intro-heading {
		font-size: 60px;
		line-height: 60px;
		margin-bottom: 40px
	}
	.shorten #shorten input.input-lg {
		width: 500px
	}
}

@media (min-width:992px) {
	.shorten #shorten input.input-lg {
		width: 650px
	}
}

@media (min-width:1010px) {
	.oline {
		max-width: 84.5%
	}
	.inlin {
		font-size: 3rem;
		margin-top: 0
	}
	.inlin-1 {
		margin-bottom: 35px;
		font-size: 1.5rem
	}
	.shorten #shorten .form-group {
		position: relative;
		margin-bottom: 15px
	}
	.dim h2.lable-1 {
		font-size: 2.2rem
	}
	section.dim {
		padding: 30px 0
	}
	.colm {
		margin-bottom: 20px
	}
}

@media (max-width:1009px) {
	.inlin {
		font-size: 1.8rem;
		margin-top: 30px
	}
	.inlin-1 {
		margin-bottom: 40px;
		font-size: 18px
	}
	.shorten #shorten .form-group {
		position: relative;
		margin-bottom: 15px
	}
	.step {
		padding: 10px 0
	}
	section.contact span {
		display: block
	}
}

.inlin {
	line-height: 1.2;
	color: #fff;
	font-weight: 700;
	padding: 5% 15px 0;
	display: block;
	margin-bottom: .5rem
}

.inlin-1 {
	color: rgba(255, 255, 255, .7);
	padding: 0 15px;
	display: block
}

.message-area.normal-bg {
	background-color: #162239;
	padding-top: 80px;
	padding-bottom: 80px
}

.message-area.normal-bg .text-other-color2,
.message-area.normal-bg .text-purple-dark {
	font-weight: 300
}

.text-other-color1 {
	font-size: 25px;
	font-weight: 400;
	color: #fff
}

.text-other-color2 {
	font-size: 25px;
	font-weight: 400;
	color: #f7f7f7
}

.message-area .buttons-holder {
	text-align: right;
	padding-top: 10px
}

.ybtn {
	border: 0;
	border-radius: 10px;
	font-size: 15px;
	font-weight: 600;
	text-transform: uppercase;
	padding: 9px 26px;
	margin: 5px 10px;
	display: inline-block;
	white-space: nowrap;
	-webkit-transition: all .3s ease 0;
	transition: all .3s ease 0;
	-webkit-box-shadow: 0 9px 18px rgba(0, 0, 0, .08);
	box-shadow: 0 9px 18px rgba(0, 0, 0, .08)
}

.ybtn-accent-color {
	color: #fff;
	background-color: #5542b0
}

.ybtn-accent-color:focus,
.ybtn-accent-color:hover {
	color: #fff;
	background-color: #00ea8a
}

.ybtn-shadow {
	-webkit-box-shadow: 0 9px 18px rgba(0, 0, 0, .08);
	box-shadow: 0 9px 18px rgba(0, 0, 0, .08)
}

.ybtn-white {
	color: #464646;
	background-color: #fff
}

.ybtn-white:focus,
.ybtn-white:hover {
	color: #fff;
	background-color: #00ea8a
}

@media(max-width:992px) {
	.message-area .buttons-holder {
		text-align: left
	}
	.message-area .buttons-holder .ybtn {
		margin-left: 0
	}
}

.payments {
	border-top: 1px solid #eee;
	padding-bottom: 0;
	position: relative;
	display: block;
	background-color: #f9f9fd;
	padding-top: 0
}

.payments img {
	height: 19px;
	margin: 20px;
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
	opacity: .5;
	-o-transition: .5s ease;
	transition: .5s ease;
	-webkit-transition: .5s ease;
	-moz-transition: .5s ease
}

.payments img.payeer {
	height: 12px
}

.payments img:hover {
	-webkit-filter: grayscale(0);
	filter: grayscale(0);
	opacity: 1;
	-o-transition: .5s ease;
	transition: .5s ease;
	-webkit-transition: .5s ease;
	-moz-transition: .5s ease;
	cursor: pointer
}

#footer {
	background-color: #0f121c;
	padding-top: 90px;
	padding-bottom: 70px;
	text-align: center
}

#footer .footer-menu-holder {
	display: inline-block;
	text-align: left;
	min-width: 130px
}

#footer h4 {
	font-size: 17px;
	color: #fff;
	margin: 0;
	margin-bottom: 20px;
	font-weight: 700
}

#footer ul.footer-menu {
	list-style: none;
	padding: 0;
	margin: 0
}

#footer ul.footer-menu li {
	margin-bottom: 15px
}

#footer ul.footer-menu li a {
	color: #fff;
	font-size: 15px;
	font-weight: 300;
	padding: 3px 12px;
	margin-left: -11px;
	border-radius: 12px;
	cursor: pointer;
	-webkit-transition: all .3s ease 0;
	transition: all .3s ease 0
}

#footer ul.footer-menu li a:focus,
#footer ul.footer-menu li a:hover {
	color: #10182f;
	background-color: #fff;
	-webkit-box-shadow: 0 8px 18px rgba(0, 0, 0, .08);
	box-shadow: 0 8px 18px rgba(0, 0, 0, .08);
	text-decoration: none
}

#footer ul.footer-menu li.active a {
	color: #00ea8a;
	background-color: #fff;
	-webkit-box-shadow: 0 8px 18px rgba(0, 0, 0, .08);
	box-shadow: 0 8px 18px rgba(0, 0, 0, .08);
	text-decoration: none
}

#footer .social-menu {
	padding: 0;
	margin: 0;
	list-style: none
}

#footer .social-menu li {
	margin-bottom: 15px;
	text-align: left
}

#footer .social-menu li a {
	display: inline-block;
	-webkit-transition: all .3s ease 0;
	transition: all .3s ease 0
}

#footer .social-menu li a .fa {
	color: #32394c;
	font-size: 20px;
	text-align: center;
	min-width: 30px
}

#footer .social-menu li a:focus .fa,
#footer .social-menu li a:hover .fa {
	color: #fff
}

#form-section>.row {
	position: relative
}

@media(max-width:768px) {
	.combine .tools {
		padding: 30px 15px
	}
	.combine .tools .flex .list {
		width: 100%;
		margin: 15px 0
	}
	.ybtn:focus,
	.ybtn:hover {
		text-decoration: none
	}
	#footer .footer-menu-holder {
		margin-bottom: 10px
	}
	#footer .social-menu li {
		text-align: center;
		display: inline-block;
		margin: 20px 10px
	}
	#footer ul.footer-menu li {
		margin-bottom: 10px
	}
	#footer ul.footer-menu li a {
		font-size: 13px
	}
}

@font-face {
	font-family: Linearicons-Free;
	src: url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.eot);
	src: url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.eot?#iefix) format('embedded-opentype'), url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.woff2) format('woff2'), url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.ttf) format('truetype'), url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.woff) format('woff'), url(https://cdn.linearicons.com/free/1.0.0/Linearicons-Free.svg#Linearicons-Free) format('svg');
	font-weight: 400;
	font-style: normal
}

.lnr {
	font-family: Linearicons-Free;
	speak: none;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale
}

.lnr-arrow-right:before {
	content: "\e87a"
}

section.reviews {
	background: #121d33;
	padding: 80px 0;
	position: relative;
	padding-top: 150px
}

section.reviews i {
	font-size: 150px;
	position: absolute;
	color: #667684;
	top: 20px
}

.high-rop {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-right: -15px;
	margin-left: -15px
}

.testim {
	-webkit-box-shadow: 0 0 50px 5px rgba(0, 0, 0, .29);
	box-shadow: 0 0 50px 5px rgba(0, 0, 0, .29);
	border-radius: 3px;
	background: #121d33;
	padding: 15px;
	margin-left: 20px;
	width: 25%
}

.testim h5 {
	display: block;
	position: relative;
	padding-left: 50px;
	color: #91d5ce
}

.testim h5 i {
	position: absolute;
	font-size: 35px;
	color: #7bc6b4;
	left: 0;
	margin-right: 10px;
	top: 4px
}

.testim h5 small {
	display: block;
	font-size: 12px;
	margin-top: 2px;
	font-weight: 600;
	color: rgba(255, 255, 255, .68)
}

.testim p {
	margin-top: 15px;
	color: #91a6c1;
	font-weight: 500;
	margin-bottom: 0;
	font-size: 17px;
	display: block;
	text-align: center
}

@media all and (max-width:1132px) {
	section.reviews .flex .testim,
	section.reviews .flex .top {
		width: calc(50% - 20px);
		margin-left: 20px;
		margin-bottom: 20px
	}
	section.reviews {
		top: -30px;
		padding: 0 0;
		padding-top: 150px;
		padding-bottom: 30px
	}
}

@media only screen and (max-width:768px) {
	.top {
		text-align: center;
		width: 100%;
		display: block;
		margin: 0 0 20px
	}
	.testim {
		margin: 15px 0;
		width: 100%;
		margin: 0 0 15px
	}
	section.reviews .flex .testim,
	section.reviews .flex .top {
		width: 100%;
		margin: 0 0 20px
	}
	#footer .social-menu li a .fa {
		font-size: 28px;
		min-width: 35px
	}
}

.payment-methods img {
	height: 20px
}

.dashboard a,
.dashboard a:hover,
.login a,
.login a:hover {
	padding: .38rem 1rem!important;
	border: 2px solid #7ace4c;
	border-radius: 30px;
	color: rgba(255, 255, 255, .7);
	background-color: #7ace4c!important;
	margin: 12px 5px;
	border: none!important
}

.signup a,
.signup a:hover {
	padding: .38rem 1rem!important;
	border: 2px solid #ff6bc1;
	border-radius: 21px;
	color: rgba(255, 255, 255, .7);
	background-color: #ff6bc1!important;
	margin: 12px 5px;
	border: none!important
}

.fa-facebook {
	color: #3b5998!important
}

.fa-instagram {
	background: radial-gradient(circle at 30% 107%, #fdf497 0, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285aeb 90%);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent
}

.fa-snapchat {
	color: #fffc00!important
}

.fa-twitter {
	color: #1da1f2!important
}

.fa-youtube-play {
	color: red!important
}

.fa-google-plus {
	color: #db4a39!important
}

.lable-1::after {
	content: '';
	display: block;
	width: 100px;
	height: 6px;
	background: #00ea8a;
	border-radius: 4px;
	margin: 0 auto;
	margin-top: 0;
	margin-top: 15px
}

section h2.section-heading {
	margin-top: 10px;
	font-weight: 600;
	font-size: 2.2rem;
	margin-bottom: 15px;
	color: #fff;
	position: relative;
	margin-top: -20px
}

@media (max-width:767px) {
	section {
		padding: 20px 0
	}
	section.dim {
		padding: 0
	}
	.dashboard a,
	.dashboard a:hover,
	.login a,
	.login a:hover,
	.signup a,
	.signup a:hover {
		margin-left: 2rem;
		margin-right: 2rem;
		text-align: center;
		margin-bottom: 15px;
		margin-top: 15px;
		padding: .45rem 1rem!important
	}
}

:focus {
	outline: 0!important
}