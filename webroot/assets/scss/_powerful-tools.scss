/*===========================
    05.Powerful Tools css 
===========================*/

.powerful-tools-area {
    position: relative;
    overflow: hidden;

    & .powerful-shape{
        position: absolute;

        &.shape-1{
              width: 488px;
              height: 488px;
              background-color: #f2fcfc;
              border-radius: 50px;
              @include transform(rotate(45deg));
              top: 18%;
              left: 10%;

              @media #{$xs}{
                  width: 320px;
                  height: 320px;
              }
        }
        &.shape-2{
              top: 30px;
              right: 3%;

              @include animation(rotated 15s infinite linear);

              @media #{$lg}{
                  width: 150px;
              }
              @media #{$md}{
                  width: 200px;
              }
              @media #{$xs}{
                  width: 150px;
              }
        }
    }
}
@-webkit-keyframes rotated {
    0%   {
        @include transform(rotate(0));
    }
    100% {
        @include transform(rotate(360deg));
    }
}
@keyframes rotated {
    0%   {
        @include transform(rotate(0));
    }
    100% {
        @include transform(rotate(360deg));
    }
}
  


.powerful-tools-wrapper{
    padding-top: 55px;
    padding-bottom: 110px;
    border-bottom: 1px solid #d7dbe7;
}

.powerful-image{
    & .image{
        @include transform(rotate(-15deg));
        margin-left: -75px;
        @include animation(ImgBounce2 2s ease-in-out infinite alternate);

        @media #{$lg}{
            margin-left: -50px;
        }
        @media #{$md}{
            margin-left: 0;
        }
        @media #{$xs}{
            margin-left: 0;
        }
    }
}

@-webkit-keyframes ImgBounce2 {
	0% {
		@include transform(translateY(0) rotate(-15deg));
	}

	100% {
        @include transform(translateY(-20px) rotate(-15deg));
	}
}

@keyframes ImgBounce {
	0% {
		@include transform(translateY(0) rotate(-15deg));
	}

	100% {
        @include transform(translateY(-20px) rotate(-15deg));
	}
}

.powerful-tools-content{
    & .powerful-content-wrapper{
        padding-top: 38px;

        & p{
            font-size: 22px;
            font-weight: 500;
            color: $body-color;
            line-height: 40px;

            @media #{$xs}{
                font-size: 16px;
                line-height: 28px;
            }
        }
        & .content-list{
            padding-top: 35px;

            & li{
                font-size: 18px;
                font-weight: 500;
                color: $body-color;
                margin-top: 10px;
                position: relative;
                padding-left: 25px;

                @media #{$xs}{
                    font-size: 16px;
                    line-height: 28px;
                }

                & i{
                    color: $theme-color;
                    position: absolute;
                    top: 5px;
                    left: 0;
                }
            }
        }
    }
    & .main-btn{
        padding: 0 50px;
        margin-top: 52px;
        height: 70px;
        line-height: 70px;

        @media #{$lg}{
            height: 50px;
            line-height: 50px;
        }
        @media #{$md}{
            height: 50px;
            line-height: 50px;
        }
        @media #{$xs}{
            height: 45px;
            line-height: 45px;
            padding: 0 30px;
        }
    }
}


