/*===========================
    13.Brand & Download css 
===========================*/

.brand-download-area {
      position: relative;
      z-index: 5;

      &::before{
            position: absolute;
            content: '';
            top: 0;
            right: 0;
            width: 30%;
            height: 100%;
            background-image: url(../images/shape/shape-4.png);
            background-size: 100% 100%;
            background-position: center right;
            z-index: -1;
      }
}

/*===== Brand Style =====*/

.brand-area{
      padding-top: 120px;
      padding-bottom: 120px;
      background-color: $white;
      border-bottom: 1px solid #d7dbe7;
}

.single-brand{
      text-align: center;

      & img{
            opacity: 0.2;
            @include transition(0.3s);          
      }

      &:hover{
            & img{
                  opacity: 0.6;
            }
      }
}



/*===== Download Style =====*/

.download-area{
      padding-top: 110px;
      overflow: hidden;
}

.download-content{
      & .title{
            font-size: 50px;
            font-weight: 800;
            color: $heading-color;

            @media #{$lg}{
                  font-size: 40px;
            }
            @media #{$xs}{
                  font-size: 24px;
            }
      }
      & p{
            font-size: 18px;
            color: #666e82;
            font-style: italic;
            padding-top: 15px;
            font-weight: 500;

            @media #{$lg}{
                  font-size: 16px;
                  line-height: 28px;
            }
            @media #{$xs}{
                  font-size: 16px;
                  line-height: 28px;
            }
      }

      & .download-btn {
            padding-top: 25px;

            & li {
                  margin-top: 10px;
                  display: inline-block;
                  margin-right: 18px;
  
                  &:last-child{
                      margin-right: 0;
                  }

                & a {
                    padding-left: 70px;
                    padding-right: 40px;
                    padding-top: 17px;
                    padding-bottom: 17px;
                    position: relative;
                    border-radius: 5px;
                    @include transition(0.3s);

                    @media #{$lg}{
                        padding-top: 10px;
                        padding-bottom: 10px;
                        padding-left: 60px;
                        padding-right: 30px;
                    }
                    @media #{$xs}{
                        padding-top: 10px;
                        padding-bottom: 10px;
                        padding-left: 60px;
                        padding-right: 30px;
                    }

                    & i {
                        font-size: 28px;
                        color: $white;
                        position: absolute;
                        top: 50%;
                        left: 40px;
                        @include transform(translateY(-50%));

                        @media #{$lg}{
                              left: 30px;
                              font-size: 24px;
                          }
                        @media #{$xs}{
                              left: 30px;
                              font-size: 24px;
                          }
                    }

                    & .text-1 {
                        font-size: 14px;
                        font-weight: 500;
                        color: $white;
                        display: block;
                    }

                    & .text-2 {
                        font-size: 18px;
                        font-weight: 700;
                        color: $white;
                        display: block;
                    }

                    &.google-play {
                        background-color: $theme-color;

                        &:hover{
                            background-color: #666e82;
                        }
                    }
                    &.apple-store {
                        background-color: #666e82;

                        &:hover{
                            background-color: $theme-color;
                        }
                    }
                }
            }
      }
}

.download-image{
      @media #{$md}{
            margin-top: 50px;
      }
      @media #{$xs}{
            margin-top: 50px;
      }
}

