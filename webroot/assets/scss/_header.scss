


/*===========================
       02.Header css 
===========================*/

.header-area{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    padding: 23px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);

    &.sticky{
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 99;
        margin-top: 0;
        @include animation(sticky 1s);
        background-color: $white;
    }
}

.navbar{
    padding: 0;    

    & .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    & .navbar-brand{}

    .navbar-toggler {
        padding: 0;
    
        & .toggler-icon {
            width: 30px;
            height: 2px;
            background-color: $white;
            display: block;
            margin: 5px 0;
            position: relative;
            @include transition(0.3s);
        }
    
        &.active {
            & .toggler-icon {
                &:nth-of-type(1) {
                    @include transform(rotate(45deg));
                    top: 7px;
                }
    
                &:nth-of-type(2) {
                    opacity: 0;
                }
    
                &:nth-of-type(3) {
                    @include transform(rotate(135deg));
                    top: -7px;
                }
            }
        }
    }

    .navbar-collapse {
        @media #{$md} {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: $white;
            z-index: 9;
            @include box-shadow (0px 15px 20px 0px rgba(0, 0, 0, 0.1));
            padding: 12px;
            @include transition(0.3s);
            margin-top: 23px;
        }
        @media #{$xs} {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: $white;
            z-index: 9;
            @include box-shadow (0px 15px 20px 0px rgba(0, 0, 0, 0.1));
            padding: 12px;
            @include transition(0.3s);
            margin-top: 23px;
        }
    }

    & .navbar-nav{
        & li{
            margin-right: 50px;
            position: relative;
            padding: 15px 0;

            @media #{$lg}{
                margin-right: 40px;
            }
            @media #{$md} {
                padding: 0;
                margin-right: 0;
            }
            @media #{$xs} {
                padding: 0;
                margin-right: 0;
            }

            & a{
                font-size: 16px;
                font-weight: 700;
                position: relative;      
                color: $white;       
                @include transition(0.3s);  
                
                @media #{$md} {
                    color: $heading-color;
                    display: block;
                    padding: 5px 10px;
                }
                @media #{$xs} {
                    color: $heading-color;
                    display: block;
                    padding: 5px 10px;
                }

                &::before{
                    position: absolute;
                    content: '';
                    width: 0;
                    height: 1px;
                    background-color: $white;
                    bottom: 2px;
                    left: 0;
                    @include transition(0.3s);

                    @media #{$md} {
                        display: none;
                    }
                    @media #{$xs} {
                        display: none;
                    }
                }

                & .sub-nav-toggler{
                    display: none;
    
                    @media #{$md} {
                        display: block;
                        position: absolute;
                        right: 0;
                        top: 0;
                        background: none;
                        color: $body-color;
                        font-size: 16px;
                        border: 0;
                        width: 35px;
                        height: 35px;
                    }
                    @media #{$xs} {
                        display: block;
                        position: absolute;
                        right: 0;
                        top: 0;
                        background: none;
                        color: $body-color;
                        font-size: 16px;
                        border: 0;
                        width: 35px;
                        height: 35px;
                    }
    
                    & span{
                        width: 8px;
                        height: 8px;
                        border-left: 1px solid $black;
                        border-bottom: 1px solid $black;
                        @include transform(rotate(-45deg));
                        position: relative;
                        top: -5px;
                    }
                }
            }

            &.active,
            &:hover{
                & > a{
                    &::before{
                        width: 100%;
                    }
                }
            }

            & .sub-menu{
                position: absolute;
                top: 120%;
                left: 50%;
                @include transform(translateX(-50%));
                width: 190px;
                background-color: $white;
                border-top: 2px solid $heading-color;
                @include box-shadow (0px 10px 30px 0px rgba(0, 0, 0, 0.05));
                @include transition(0.3s);
                padding: 10px 0;    
                opacity: 0;
                visibility: hidden;    
                
                @media #{$md} {
                    position: relative;
                    width: 100%;
                    top: 0;
                    left: 0;
                    @include transform(translateX(0));
                    display: none;
                    opacity: 1;
                    visibility: visible;
                    @include box-shadow (none);
                    @include transition(0s);
                    border-top: 0;
                    padding: 0 10px;
                }
                @media #{$xs} {
                    position: relative;
                    width: 100%;
                    top: 0;
                    left: 0;
                    @include transform(translateX(0));
                    display: none;
                    opacity: 1;
                    visibility: visible;
                    @include box-shadow (none);
                    @include transition(0s);
                    border-top: 0;
                    padding: 0 10px;
                }

                & li{
                    padding: 0;
                    margin-right: 0;

                    & a{
                        color: $heading-color;
                        padding: 6px 20px;
                        font-size: 14px;
                        display: block;

                        &::before{
                            display: none;
                        }
                    }

                    &:hover{
                        & > a{
                            padding-left: 23px;
                            color: $theme-color;
                        }
                    }
                }
            }

            &:hover{
                & .sub-menu{
                    top: 100%;
                    opacity: 1;
                    visibility: visible;

                    @media #{$md} {
                        top: 0;
                        left: 0;
                    }
                    @media #{$xs} {
                        top: 0;
                        left: 0;
                    }
                }
            }
        }
    }
    & .navbar-btn{
        @media #{$md} {
            padding-top: 10px;
        }
        @media #{$xs} {
            padding-top: 10px;
        }

        & .main-btn{
            @media #{$md} {
                display: block;
            }
            @media #{$xs} {
                display: block;
            }
        }
    }
}


@-webkit-keyframes sticky {
    0%{
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    100%{
        -webkit-transform: translateY(0%);
        transform: translateY(0%)
    }
}

@keyframes sticky {
    0%{
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    100%{
        -webkit-transform: translateY(0%);
        transform: translateY(0%)
    }
}


.sticky{
    & .navbar{    
        & .navbar-toggler {        
            & .toggler-icon {
                background-color: $heading-color;
            }
        }    
        & .navbar-nav{
            & li{    
                & a{     
                    color: $heading-color;

                    &::before{
                        background-color: $heading-color;
                    }    
                }
            }
        }
    }
}


 