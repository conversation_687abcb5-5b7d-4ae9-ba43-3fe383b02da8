/*===========================
    07.Everything css 
===========================*/

.everything-area {
    background-color: #fff8f6;
    padding-top: 70px;
    padding-bottom: 120px;
    position: relative;
    overflow: hidden;

    & .everything-shape{
        position: absolute;

        &.shape-1{
              width: 588px;
              height: 588px;
              background-color: $theme-color;
              border-radius: 50px;
              @include transform(rotate(45deg));
              top: 13%;
              left: 0;

              @media #{$lg}{
                  width: 420px;
                  height: 420px;
                  top: 25%;
              }
              @media #{$md}{
                  top: 7%;
              }
              @media #{$xs}{
                  width: 320px;
                  height: 320px;
                  top: 5%;
              }
        }
        &.shape-2{
            width: 488px;
            height: 488px;
            background-color: $white;
            border-radius: 50px;
            @include transform(rotate(45deg));
            top: -30%;
            right: 0;

            @media #{$lg}{
                width: 388px;
                height: 388px;
            }
            @media #{$xs}{
                width: 388px;
                height: 388px;
            }
        }
    }
}

.everything-image{
    & .image{
        position: relative;
        @include animation(ImgBounce3 2s ease-in-out infinite alternate);

        @media #{$desktop}{
            left: -50px;
        }

        & img{
            width: 100%;
        }
    }
}
@-webkit-keyframes ImgBounce3 {
	0% {
		@include transform(translateY(0));
	}

	100% {
        @include transform(translateY(-20px));
	}
}

@keyframes ImgBounce {
	0% {
		@include transform(translateY(0));
	}

	100% {
        @include transform(translateY(-20px));
	}
}

.everything-content-wrapper{
    & .everything-content{
        & p{
            font-size: 18px;
            font-weight: 500;
            line-height: 34px;

            @media #{$lg}{
                font-size: 16px;
                line-height: 28px;
            }
            @media #{$xs}{
                font-size: 16px;
                line-height: 28px;
            }
        }

        & .everything-items{
            & li{
                display: -webkit-flex;
                display: -moz-flex;
                display: -ms-flex;
                display: -o-flex;
                display: flex;
                margin-top: 45px;

                & span{
                    width: 55px;
                    height: 55px;
                    line-height: 55px;
                    border-radius: 50%;
                    text-align: center;
                    font-size: 18px;
                    font-weight: 700;
                    font-family: $font-1;
                    color: $white;
                    margin-top: 3px;
                }

                & p{
                    -webkit-flex: 1;
                    -moz-flex: 1;
                    -ms-flex: 1;
                    flex: 1;
                    padding-left: 15px;
                }

                &:nth-child(3n+1){
                    & span{
                        background-color: #ee539b;
                    }
                }
                &:nth-child(3n+2){
                    & span{
                        background-color: #9364d4;
                    }
                }
                &:nth-child(3n+3){
                    & span{
                        background-color: #5dd8d3;
                    }
                }
            }
        }
        & .main-btn{
            padding: 0 50px;
            margin-top: 52px;
            height: 70px;
            line-height: 70px;
    
            @media #{$lg}{
                height: 50px;
                line-height: 50px;
            }
            @media #{$md}{
                height: 50px;
                line-height: 50px;
            }
            @media #{$xs}{
                height: 45px;
                line-height: 45px;
                padding: 0 30px;
            }
        }
    }
}



