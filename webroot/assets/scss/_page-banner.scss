/*===========================
      15.Page Banner css 
===========================*/

.page-banner-area {
        position: relative;
        z-index: 5;

        & .page-banner-shape{
                position: absolute;
                
                &.shape-1{
                    width: 588px;
                    height: 588px;
                    background: -moz-linear-gradient( 70deg, rgb(116,86,204) 0%, rgb(207,147,213) 100%);
                    background: -webkit-linear-gradient( 70deg, rgb(116,86,204) 0%, rgb(207,147,213) 100%);
                    background: -ms-linear-gradient( 70deg, rgb(116,86,204) 0%, rgb(207,147,213) 100%);
                    border-radius: 50px;
                    @include transform(rotate(-45deg));
                    top: -63%;
                    left: -3%;
                    opacity: 0.7;
        
                    @media #{$lg}{
                        width: 388px;
                        height: 388px;
                        top: -40%;
                    }
                    @media #{$md}{
                        width: 320px;
                        height: 320px;
                        top: -40%;
                    }
                    @media #{$xs}{
                        width: 188px;
                        height: 188px;
                        top: -2%;
                    }
                    @media #{$sm}{
                        width: 288px;
                        height: 288px;
                        top: -30%;
                    }
                }
          }

        &::before{
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: -moz-linear-gradient( 152deg, rgb(108,81,203) 0%, rgb(208,148,213) 100%);
                background: -webkit-linear-gradient( 152deg, rgb(108,81,203) 0%, rgb(208,148,213) 100%);
                background: -ms-linear-gradient( 152deg, rgb(108,81,203) 0%, rgb(208,148,213) 100%);
                opacity: 0.9;
                z-index: -5;
        }
}
      

.page-banner-content{
        padding-top: 225px;
        padding-bottom: 130px;

        @media #{$md}{
                padding-top: 195px;
                padding-bottom: 100px;
        }
        @media #{$xs}{
                padding-top: 195px;
                padding-bottom: 100px;
        }

        & .breadcrumb{
                border-radius: 0;
                padding: 0;
                margin-bottom: 0;
                background: none;

                & .breadcrumb-item{
                        font-size: 16px;
                        font-weight: 600;
                        color: $white;
                        position: relative;

                        & + .breadcrumb-item{
                                padding-left: 20px;

                                &::before{
                                        position: absolute;
                                        content: '';
                                        width: 4px;
                                        height: 4px;
                                        border-radius: 50%;
                                        background-color: $white;
                                        bottom: 4px;
                                        left: 7px;
                                        padding: 0;
                                }
                        }
                        
                        & a{
                                color: $white;
                        }
                }
        }

        & .title{
                font-size: 46px;
                font-weight: 800;
                color: $white;
                margin-top: 5px;

                @media #{$xs}{
                        font-size: 30px;
                        margin-top: 10px;
                }
        }
}



