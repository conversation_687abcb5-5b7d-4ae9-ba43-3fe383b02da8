/*===========================
      09.Testimonial css 
===========================*/

.testimonial-area {
        padding-top: 120px;
        padding-bottom: 120px;
        position: relative;
        z-index: 5;
        overflow: hidden;

        & .testimonial-shape{
                position: absolute;
        
                &.shape-1{
                      width: 488px;
                      height: 488px;
                      background-color: $white;
                      border-radius: 50px;
                      @include transform(rotate(45deg));
                      top: 0%;
                      left: -13%;
                      z-index: -1;

                      @media #{$lg}{
                              width: 420px;
                              height: 420px;
                      }
                      @media #{$md}{
                              width: 380px;
                              height: 380px;
                      }
                      @media #{$xs}{
                        width: 220px;
                        height: 220px;
                        }
                }
                &.shape-2{
                    width: 488px;
                    height: 488px;
                    background-color: $white;
                    border-radius: 50px;
                    @include transform(rotate(45deg));
                    bottom: -23%;
                    right: 0;
                    z-index: -1;

                    @media #{$lg}{
                        width: 420px;
                        height: 420px;
                    }
                    @media #{$md}{
                        width: 380px;
                        height: 380px;
                    }
                    @media #{$xs}{
                        width: 220px;
                        height: 220px;
                        bottom: -12%;
                    }
                }
        }
}

.testimonial-wrapper{
        max-width: 1920px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        @include animation(tesiMove linear infinite 5s);

        @media #{$xs}{
                display: none;
        }

        & .author-1{
                position: absolute;
                bottom: 21%;
                left: 11%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 75px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-2{
                position: absolute;
                top: 30%;
                left: 23%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 75px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        
        & .author-3{
                position: absolute;
                top: 43%;
                left: 10%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 50px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-4{
                position: absolute;
                bottom: 29%;
                left: 26%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 50px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-5{
                position: absolute;
                top: 40%;
                right: 27%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 75px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-6{
                position: absolute;
                top: 46%;
                right: 10%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 50px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-7{
                position: absolute;
                bottom: 16%;
                right: 24%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 75px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
        & .author-8{
                position: absolute;
                bottom: 24%;
                right: 6%;
                padding: 3px;
                background-color: $white;
                border-radius: 50%;
                width: 50px;
                @include box-shadow (0px 20px 20px 0px rgba(0, 0, 0, 0.15));
                
                & img{
                        border-radius: 50%;
                        width: 100%;
                }
        }
}


@keyframes tesiMove {
	0% {
		transform: rotate(0deg);
	}

	15% {
		transform: rotate(2deg);
	}

	33% {
		transform: rotate(4deg);
	}

	66% {
		transform: rotate(2deg);
	}

	100% {
		transform: rotate(0deg);
	}
}

.testimonial-bg{
        background-image: url(../images/shape/shape-3.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        padding-top: 150px;
        padding-bottom: 155px;
        margin-top: 60px;

        @media #{$xs}{
                background: $theme-color;
                padding-top: 45px;
                padding-bottom: 45px;
                padding-left: 15px;
                padding-right: 15px;
                border-radius: 5px;
        }
}

.testimonial-active{
        max-width: 540px;
        margin: 0 auto;
}

.single-testimonial{
        & p{
                font-size: 20px;
                font-weight: 600;
                color: $white;
                line-height: 40px;

                @media #{$xs}{
                        font-size: 16px;
                        line-height: 28px;
                }
        }
        & .author-name{
                font-weight: 700;
                font-size: 20px;
                color: $white;
                margin-top: 50px;
        }
}
