/*===========================
      10.Screenshot css 
===========================*/

.screenshot-area {
      padding-top: 120px;
}

.screenshot-active{
      padding: 92px 0 92px;
      position: relative;
      margin-top: 46px;

      &::before{
            position: absolute;
            content: '';
            width: 21%;
            left: 50%;
            @include transform(translateX(-50%));
            top: 0;
            height: 100%;
            background-image: url(../images/screens/frame.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            z-index: 5;

            @media #{$lg}{
                  width: 38%;
            }
            @media #{$md}{
                  width: 38%;
            }
            @media #{$xs}{
                  width: 101%;
            }
            @media #{$sm}{
                  width: 38%;
            }
      }

      & .single-screenshot{
            & img{
                  width: 100%;
            }
      }
}

.screenshot-pagination{
      position: relative;
      margin-top: 45px;
      text-align: center;

      & .swiper-pagination-bullet{
            background: $heading-color;
            margin: 0 5px;
            @include transition(0.3s);

            &.swiper-pagination-bullet-active{
                  background-color: $theme-color;
                  @include transform(scale(1.7));
            }
      }
}

