	/*
  	Flaticon icon font: Flaticon
  	Creation date: 24/07/2020 17:14
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../../fonts/Flaticon.eot");
  src: url("../../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../../fonts/Flaticon.woff2") format("woff2"),
       url("../../fonts/Flaticon.woff") format("woff"),
       url("../../fonts/Flaticon.ttf") format("truetype"),
       url("../../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-smartphone-2:before { content: "\f100"; }
.flaticon-smartphone:before { content: "\f101"; }
.flaticon-smartphone-1:before { content: "\f102"; }
.flaticon-strategy:before { content: "\f103"; }
.flaticon-training:before { content: "\f104"; }
.flaticon-human-resources:before { content: "\f105"; }
.flaticon-send:before { content: "\f106"; }
.flaticon-shuttle:before { content: "\f107"; }
.flaticon-plane:before { content: "\f108"; }