!function(t){"use strict";t('a.js-scroll-trigger[href*="#"]:not([href="#"])').click(function(){if(location.pathname.replace(/^\//,"")==this.pathname.replace(/^\//,"")&&location.hostname==this.hostname){var e=t(this.hash);if((e=e.length?e:t("[name="+this.hash.slice(1)+"]")).length)return t("html, body").animate({scrollTop:e.offset().top-54},1e3,"easeInOutExpo"),!1}}),t(".js-scroll-trigger").click(function(){t(".navbar-collapse").collapse("hide")}),t("body").scrollspy({target:"#mainNav",offset:54})}(jQuery),$(window).scroll(function(){500<$(window).scrollTop()?$("#back2Top").fadeIn():$("#back2Top").fadeOut()}),$(document).ready(function(){$("#back2Top").click(function(e){return e.preventDefault(),$("html, body").animate({scrollTop:0},"slow"),!1})}),function(t){"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&"object"==typeof module.exports?exports=t(require("jquery")):t(jQuery)}(function(t){t.easing.jswing=t.easing.swing;var a=Math.pow,i=Math.sqrt,n=Math.sin,s=Math.cos,r=Math.PI,c=1.70158,o=1.525*c,l=1+c,u=2*r/3,v=2*r/4.5;function p(e){var t=7.5625,a=2.75;return e<1/a?t*e*e:e<2/a?t*(e-=1.5/a)*e+.75:e<2.5/a?t*(e-=2.25/a)*e+.9375:t*(e-=2.625/a)*e+.984375}t.extend(t.easing,{def:"easeOutQuad",swing:function(e){return t.easing[t.easing.def](e)},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return 1-(1-e)*(1-e)},easeInOutQuad:function(e){return e<.5?2*e*e:1-a(-2*e+2,2)/2},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return 1-a(1-e,3)},easeInOutCubic:function(e){return e<.5?4*e*e*e:1-a(-2*e+2,3)/2},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return 1-a(1-e,4)},easeInOutQuart:function(e){return e<.5?8*e*e*e*e:1-a(-2*e+2,4)/2},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return 1-a(1-e,5)},easeInOutQuint:function(e){return e<.5?16*e*e*e*e*e:1-a(-2*e+2,5)/2},easeInSine:function(e){return 1-s(e*r/2)},easeOutSine:function(e){return n(e*r/2)},easeInOutSine:function(e){return-(s(r*e)-1)/2},easeInExpo:function(e){return 0===e?0:a(2,10*e-10)},easeOutExpo:function(e){return 1===e?1:1-a(2,-10*e)},easeInOutExpo:function(e){return 0===e?0:1===e?1:e<.5?a(2,20*e-10)/2:(2-a(2,-20*e+10))/2},easeInCirc:function(e){return 1-i(1-a(e,2))},easeOutCirc:function(e){return i(1-a(e-1,2))},easeInOutCirc:function(e){return e<.5?(1-i(1-a(2*e,2)))/2:(i(1-a(-2*e+2,2))+1)/2},easeInElastic:function(e){return 0===e?0:1===e?1:-a(2,10*e-10)*n((10*e-10.75)*u)},easeOutElastic:function(e){return 0===e?0:1===e?1:a(2,-10*e)*n((10*e-.75)*u)+1},easeInOutElastic:function(e){return 0===e?0:1===e?1:e<.5?-a(2,20*e-10)*n((20*e-11.125)*v)/2:a(2,-20*e+10)*n((20*e-11.125)*v)/2+1},easeInBack:function(e){return l*e*e*e-c*e*e},easeOutBack:function(e){return 1+l*a(e-1,3)+c*a(e-1,2)},easeInOutBack:function(e){return e<.5?a(2*e,2)*(2*(1+o)*e-o)/2:(a(2*e-2,2)*((1+o)*(2*e-2)+o)+2)/2},easeInBounce:function(e){return 1-p(1-e)},easeOutBounce:p,easeInOutBounce:function(e){return e<.5?(1-p(1-2*e))/2:(1+p(2*e-1))/2}})});var pJS=function(e,t){var a=document.querySelector("#"+e+" > .particles-js-canvas-el");this.pJS={canvas:{el:a,w:a.offsetWidth,h:a.offsetHeight},particles:{number:{value:400,density:{enable:!0,value_area:800}},color:{value:"#fff"},shape:{type:"circle",stroke:{width:0,color:"#ff0000"},polygon:{nb_sides:5},image:{src:"",width:100,height:100}},opacity:{value:1,random:!1,anim:{enable:!1,speed:2,opacity_min:0,sync:!1}},size:{value:20,random:!1,anim:{enable:!1,speed:20,size_min:0,sync:!1}},line_linked:{enable:!0,distance:100,color:"#fff",opacity:1,width:1},move:{enable:!0,speed:2,direction:"none",random:!1,straight:!1,out_mode:"out",bounce:!1,attract:{enable:!1,rotateX:3e3,rotateY:3e3}},array:[]},interactivity:{detect_on:"canvas",events:{onhover:{enable:!0,mode:"grab"},onclick:{enable:!0,mode:"push"},resize:!0},modes:{grab:{distance:100,line_linked:{opacity:1}},bubble:{distance:200,size:80,duration:.4},repulse:{distance:200,duration:.4},push:{particles_nb:4},remove:{particles_nb:2}},mouse:{}},retina_detect:!1,fn:{interact:{},modes:{},vendors:{}},tmp:{}};var d=this.pJS;t&&Object.deepExtend(d,t),d.tmp.obj={size_value:d.particles.size.value,size_anim_speed:d.particles.size.anim.speed,move_speed:d.particles.move.speed,line_linked_distance:d.particles.line_linked.distance,line_linked_width:d.particles.line_linked.width,mode_grab_distance:d.interactivity.modes.grab.distance,mode_bubble_distance:d.interactivity.modes.bubble.distance,mode_bubble_size:d.interactivity.modes.bubble.size,mode_repulse_distance:d.interactivity.modes.repulse.distance},d.fn.retinaInit=function(){d.retina_detect&&1<window.devicePixelRatio?(d.canvas.pxratio=window.devicePixelRatio,d.tmp.retina=!0):(d.canvas.pxratio=1,d.tmp.retina=!1),d.canvas.w=d.canvas.el.offsetWidth*d.canvas.pxratio,d.canvas.h=d.canvas.el.offsetHeight*d.canvas.pxratio,d.particles.size.value=d.tmp.obj.size_value*d.canvas.pxratio,d.particles.size.anim.speed=d.tmp.obj.size_anim_speed*d.canvas.pxratio,d.particles.move.speed=d.tmp.obj.move_speed*d.canvas.pxratio,d.particles.line_linked.distance=d.tmp.obj.line_linked_distance*d.canvas.pxratio,d.interactivity.modes.grab.distance=d.tmp.obj.mode_grab_distance*d.canvas.pxratio,d.interactivity.modes.bubble.distance=d.tmp.obj.mode_bubble_distance*d.canvas.pxratio,d.particles.line_linked.width=d.tmp.obj.line_linked_width*d.canvas.pxratio,d.interactivity.modes.bubble.size=d.tmp.obj.mode_bubble_size*d.canvas.pxratio,d.interactivity.modes.repulse.distance=d.tmp.obj.mode_repulse_distance*d.canvas.pxratio},d.fn.canvasInit=function(){d.canvas.ctx=d.canvas.el.getContext("2d")},d.fn.canvasSize=function(){d.canvas.el.width=d.canvas.w,d.canvas.el.height=d.canvas.h,d&&d.interactivity.events.resize&&window.addEventListener("resize",function(){d.canvas.w=d.canvas.el.offsetWidth,d.canvas.h=d.canvas.el.offsetHeight,d.tmp.retina&&(d.canvas.w*=d.canvas.pxratio,d.canvas.h*=d.canvas.pxratio),d.canvas.el.width=d.canvas.w,d.canvas.el.height=d.canvas.h,d.particles.move.enable||(d.fn.particlesEmpty(),d.fn.particlesCreate(),d.fn.particlesDraw(),d.fn.vendors.densityAutoParticles()),d.fn.vendors.densityAutoParticles()})},d.fn.canvasPaint=function(){d.canvas.ctx.fillRect(0,0,d.canvas.w,d.canvas.h)},d.fn.canvasClear=function(){d.canvas.ctx.clearRect(0,0,d.canvas.w,d.canvas.h)},d.fn.particle=function(e,t,a){if(this.radius=(d.particles.size.random?Math.random():1)*d.particles.size.value,d.particles.size.anim.enable&&(this.size_status=!1,this.vs=d.particles.size.anim.speed/100,d.particles.size.anim.sync||(this.vs=this.vs*Math.random())),this.x=a?a.x:Math.random()*d.canvas.w,this.y=a?a.y:Math.random()*d.canvas.h,this.x>d.canvas.w-2*this.radius?this.x=this.x-this.radius:this.x<2*this.radius&&(this.x=this.x+this.radius),this.y>d.canvas.h-2*this.radius?this.y=this.y-this.radius:this.y<2*this.radius&&(this.y=this.y+this.radius),d.particles.move.bounce&&d.fn.vendors.checkOverlap(this,a),this.color={},"object"==typeof e.value)if(e.value instanceof Array){var i=e.value[Math.floor(Math.random()*d.particles.color.value.length)];this.color.rgb=hexToRgb(i)}else null!=e.value.r&&null!=e.value.g&&null!=e.value.b&&(this.color.rgb={r:e.value.r,g:e.value.g,b:e.value.b}),null!=e.value.h&&null!=e.value.s&&null!=e.value.l&&(this.color.hsl={h:e.value.h,s:e.value.s,l:e.value.l});else"random"==e.value?this.color.rgb={r:Math.floor(256*Math.random())+0,g:Math.floor(256*Math.random())+0,b:Math.floor(256*Math.random())+0}:"string"==typeof e.value&&(this.color=e,this.color.rgb=hexToRgb(this.color.value));this.opacity=(d.particles.opacity.random?Math.random():1)*d.particles.opacity.value,d.particles.opacity.anim.enable&&(this.opacity_status=!1,this.vo=d.particles.opacity.anim.speed/100,d.particles.opacity.anim.sync||(this.vo=this.vo*Math.random()));var n={};switch(d.particles.move.direction){case"top":n={x:0,y:-1};break;case"top-right":n={x:.5,y:-.5};break;case"right":n={x:1,y:-0};break;case"bottom-right":n={x:.5,y:.5};break;case"bottom":n={x:0,y:1};break;case"bottom-left":n={x:-.5,y:1};break;case"left":n={x:-1,y:0};break;case"top-left":n={x:-.5,y:-.5};break;default:n={x:0,y:0}}d.particles.move.straight?(this.vx=n.x,this.vy=n.y,d.particles.move.random&&(this.vx=this.vx*Math.random(),this.vy=this.vy*Math.random())):(this.vx=n.x+Math.random()-.5,this.vy=n.y+Math.random()-.5),this.vx_i=this.vx,this.vy_i=this.vy;var s=d.particles.shape.type;if("object"==typeof s){if(s instanceof Array){var r=s[Math.floor(Math.random()*s.length)];this.shape=r}}else this.shape=s;if("image"==this.shape){var c=d.particles.shape;this.img={src:c.image.src,ratio:c.image.width/c.image.height},this.img.ratio||(this.img.ratio=1),"svg"==d.tmp.img_type&&null!=d.tmp.source_svg&&(d.fn.vendors.createSvgImg(this),d.tmp.pushing&&(this.img.loaded=!1))}},d.fn.particle.prototype.draw=function(){var e=this;if(null!=e.radius_bubble)var t=e.radius_bubble;else t=e.radius;if(null!=e.opacity_bubble)var a=e.opacity_bubble;else a=e.opacity;if(e.color.rgb)var i="rgba("+e.color.rgb.r+","+e.color.rgb.g+","+e.color.rgb.b+","+a+")";else i="hsla("+e.color.hsl.h+","+e.color.hsl.s+"%,"+e.color.hsl.l+"%,"+a+")";switch(d.canvas.ctx.fillStyle=i,d.canvas.ctx.beginPath(),e.shape){case"circle":d.canvas.ctx.arc(e.x,e.y,t,0,2*Math.PI,!1);break;case"edge":d.canvas.ctx.rect(e.x-t,e.y-t,2*t,2*t);break;case"triangle":d.fn.vendors.drawShape(d.canvas.ctx,e.x-t,e.y+t/1.66,2*t,3,2);break;case"polygon":d.fn.vendors.drawShape(d.canvas.ctx,e.x-t/(d.particles.shape.polygon.nb_sides/3.5),e.y-t/.76,2.66*t/(d.particles.shape.polygon.nb_sides/3),d.particles.shape.polygon.nb_sides,1);break;case"star":d.fn.vendors.drawShape(d.canvas.ctx,e.x-2*t/(d.particles.shape.polygon.nb_sides/4),e.y-t/1.52,2*t*2.66/(d.particles.shape.polygon.nb_sides/3),d.particles.shape.polygon.nb_sides,2);break;case"image":;if("svg"==d.tmp.img_type)var n=e.img.obj;else n=d.tmp.img_obj;n&&d.canvas.ctx.drawImage(n,e.x-t,e.y-t,2*t,2*t/e.img.ratio)}d.canvas.ctx.closePath(),0<d.particles.shape.stroke.width&&(d.canvas.ctx.strokeStyle=d.particles.shape.stroke.color,d.canvas.ctx.lineWidth=d.particles.shape.stroke.width,d.canvas.ctx.stroke()),d.canvas.ctx.fill()},d.fn.particlesCreate=function(){for(var e=0;e<d.particles.number.value;e++)d.particles.array.push(new d.fn.particle(d.particles.color,d.particles.opacity.value))},d.fn.particlesUpdate=function(){for(var e=0;e<d.particles.array.length;e++){var t=d.particles.array[e];if(d.particles.move.enable){var a=d.particles.move.speed/2;t.x+=t.vx*a,t.y+=t.vy*a}if(d.particles.opacity.anim.enable&&(1==t.opacity_status?(t.opacity>=d.particles.opacity.value&&(t.opacity_status=!1),t.opacity+=t.vo):(t.opacity<=d.particles.opacity.anim.opacity_min&&(t.opacity_status=!0),t.opacity-=t.vo),t.opacity<0&&(t.opacity=0)),d.particles.size.anim.enable&&(1==t.size_status?(t.radius>=d.particles.size.value&&(t.size_status=!1),t.radius+=t.vs):(t.radius<=d.particles.size.anim.size_min&&(t.size_status=!0),t.radius-=t.vs),t.radius<0&&(t.radius=0)),"bounce"==d.particles.move.out_mode)var i={x_left:t.radius,x_right:d.canvas.w,y_top:t.radius,y_bottom:d.canvas.h};else i={x_left:-t.radius,x_right:d.canvas.w+t.radius,y_top:-t.radius,y_bottom:d.canvas.h+t.radius};switch(t.x-t.radius>d.canvas.w?(t.x=i.x_left,t.y=Math.random()*d.canvas.h):t.x+t.radius<0&&(t.x=i.x_right,t.y=Math.random()*d.canvas.h),t.y-t.radius>d.canvas.h?(t.y=i.y_top,t.x=Math.random()*d.canvas.w):t.y+t.radius<0&&(t.y=i.y_bottom,t.x=Math.random()*d.canvas.w),d.particles.move.out_mode){case"bounce":t.x+t.radius>d.canvas.w?t.vx=-t.vx:t.x-t.radius<0&&(t.vx=-t.vx),t.y+t.radius>d.canvas.h?t.vy=-t.vy:t.y-t.radius<0&&(t.vy=-t.vy)}if(isInArray("grab",d.interactivity.events.onhover.mode)&&d.fn.modes.grabParticle(t),(isInArray("bubble",d.interactivity.events.onhover.mode)||isInArray("bubble",d.interactivity.events.onclick.mode))&&d.fn.modes.bubbleParticle(t),(isInArray("repulse",d.interactivity.events.onhover.mode)||isInArray("repulse",d.interactivity.events.onclick.mode))&&d.fn.modes.repulseParticle(t),d.particles.line_linked.enable||d.particles.move.attract.enable)for(var n=e+1;n<d.particles.array.length;n++){var s=d.particles.array[n];d.particles.line_linked.enable&&d.fn.interact.linkParticles(t,s),d.particles.move.attract.enable&&d.fn.interact.attractParticles(t,s),d.particles.move.bounce&&d.fn.interact.bounceParticles(t,s)}}},d.fn.particlesDraw=function(){d.canvas.ctx.clearRect(0,0,d.canvas.w,d.canvas.h),d.fn.particlesUpdate();for(var e=0;e<d.particles.array.length;e++){d.particles.array[e].draw()}},d.fn.particlesEmpty=function(){d.particles.array=[]},d.fn.particlesRefresh=function(){cancelRequestAnimFrame(d.fn.checkAnimFrame),cancelRequestAnimFrame(d.fn.drawAnimFrame),d.tmp.source_svg=void 0,d.tmp.img_obj=void 0,d.tmp.count_svg=0,d.fn.particlesEmpty(),d.fn.canvasClear(),d.fn.vendors.start()},d.fn.interact.linkParticles=function(e,t){var a=e.x-t.x,i=e.y-t.y,n=Math.sqrt(a*a+i*i);if(n<=d.particles.line_linked.distance){var s=d.particles.line_linked.opacity-n/(1/d.particles.line_linked.opacity)/d.particles.line_linked.distance;if(0<s){var r=d.particles.line_linked.color_rgb_line;d.canvas.ctx.strokeStyle="rgba("+r.r+","+r.g+","+r.b+","+s+")",d.canvas.ctx.lineWidth=d.particles.line_linked.width,d.canvas.ctx.beginPath(),d.canvas.ctx.moveTo(e.x,e.y),d.canvas.ctx.lineTo(t.x,t.y),d.canvas.ctx.stroke(),d.canvas.ctx.closePath()}}},d.fn.interact.attractParticles=function(e,t){var a=e.x-t.x,i=e.y-t.y;if(Math.sqrt(a*a+i*i)<=d.particles.line_linked.distance){var n=a/(1e3*d.particles.move.attract.rotateX),s=i/(1e3*d.particles.move.attract.rotateY);e.vx-=n,e.vy-=s,t.vx+=n,t.vy+=s}},d.fn.interact.bounceParticles=function(e,t){var a=e.x-t.x,i=e.y-t.y;Math.sqrt(a*a+i*i)<=e.radius+t.radius&&(e.vx=-e.vx,e.vy=-e.vy,t.vx=-t.vx,t.vy=-t.vy)},d.fn.modes.pushParticles=function(e,t){d.tmp.pushing=!0;for(var a=0;a<e;a++)d.particles.array.push(new d.fn.particle(d.particles.color,d.particles.opacity.value,{x:t?t.pos_x:Math.random()*d.canvas.w,y:t?t.pos_y:Math.random()*d.canvas.h})),a==e-1&&(d.particles.move.enable||d.fn.particlesDraw(),d.tmp.pushing=!1)},d.fn.modes.removeParticles=function(e){d.particles.array.splice(0,e),d.particles.move.enable||d.fn.particlesDraw()},d.fn.modes.bubbleParticle=function(c){if(d.interactivity.events.onhover.enable&&isInArray("bubble",d.interactivity.events.onhover.mode)){var e=c.x-d.interactivity.mouse.pos_x,t=c.y-d.interactivity.mouse.pos_y,a=1-(o=Math.sqrt(e*e+t*t))/d.interactivity.modes.bubble.distance;function i(){c.opacity_bubble=c.opacity,c.radius_bubble=c.radius}if(o<=d.interactivity.modes.bubble.distance){if(0<=a&&"mousemove"==d.interactivity.status){if(d.interactivity.modes.bubble.size!=d.particles.size.value)if(d.interactivity.modes.bubble.size>d.particles.size.value){0<=(s=c.radius+d.interactivity.modes.bubble.size*a)&&(c.radius_bubble=s)}else{var n=c.radius-d.interactivity.modes.bubble.size,s=c.radius-n*a;c.radius_bubble=0<s?s:0}var r;if(d.interactivity.modes.bubble.opacity!=d.particles.opacity.value)if(d.interactivity.modes.bubble.opacity>d.particles.opacity.value)(r=d.interactivity.modes.bubble.opacity*a)>c.opacity&&r<=d.interactivity.modes.bubble.opacity&&(c.opacity_bubble=r);else(r=c.opacity-(d.particles.opacity.value-d.interactivity.modes.bubble.opacity)*a)<c.opacity&&r>=d.interactivity.modes.bubble.opacity&&(c.opacity_bubble=r)}}else i();"mouseleave"==d.interactivity.status&&i()}else if(d.interactivity.events.onclick.enable&&isInArray("bubble",d.interactivity.events.onclick.mode)){if(d.tmp.bubble_clicking){e=c.x-d.interactivity.mouse.click_pos_x,t=c.y-d.interactivity.mouse.click_pos_y;var o=Math.sqrt(e*e+t*t),l=((new Date).getTime()-d.interactivity.mouse.click_time)/1e3;l>d.interactivity.modes.bubble.duration&&(d.tmp.bubble_duration_end=!0),l>2*d.interactivity.modes.bubble.duration&&(d.tmp.bubble_clicking=!1,d.tmp.bubble_duration_end=!1)}function u(e,t,a,i,n){if(e!=t)if(d.tmp.bubble_duration_end)null!=a&&(r=e+(e-(i-l*(i-e)/d.interactivity.modes.bubble.duration)),"size"==n&&(c.radius_bubble=r),"opacity"==n&&(c.opacity_bubble=r));else if(o<=d.interactivity.modes.bubble.distance){if(null!=a)var s=a;else s=i;if(s!=e){var r=i-l*(i-e)/d.interactivity.modes.bubble.duration;"size"==n&&(c.radius_bubble=r),"opacity"==n&&(c.opacity_bubble=r)}}else"size"==n&&(c.radius_bubble=void 0),"opacity"==n&&(c.opacity_bubble=void 0)}d.tmp.bubble_clicking&&(u(d.interactivity.modes.bubble.size,d.particles.size.value,c.radius_bubble,c.radius,"size"),u(d.interactivity.modes.bubble.opacity,d.particles.opacity.value,c.opacity_bubble,c.opacity,"opacity"))}},d.fn.modes.repulseParticle=function(i){if(d.interactivity.events.onhover.enable&&isInArray("repulse",d.interactivity.events.onhover.mode)&&"mousemove"==d.interactivity.status){var e=i.x-d.interactivity.mouse.pos_x,t=i.y-d.interactivity.mouse.pos_y,a=Math.sqrt(e*e+t*t),n=e/a,s=t/a,r=clamp(1/(o=d.interactivity.modes.repulse.distance)*(-1*Math.pow(a/o,2)+1)*o*100,0,50),c={x:i.x+n*r,y:i.y+s*r};"bounce"==d.particles.move.out_mode?(0<c.x-i.radius&&c.x+i.radius<d.canvas.w&&(i.x=c.x),0<c.y-i.radius&&c.y+i.radius<d.canvas.h&&(i.y=c.y)):(i.x=c.x,i.y=c.y)}else if(d.interactivity.events.onclick.enable&&isInArray("repulse",d.interactivity.events.onclick.mode))if(d.tmp.repulse_finish||(d.tmp.repulse_count++,d.tmp.repulse_count==d.particles.array.length&&(d.tmp.repulse_finish=!0)),d.tmp.repulse_clicking){var o=Math.pow(d.interactivity.modes.repulse.distance/6,3),l=d.interactivity.mouse.click_pos_x-i.x,u=d.interactivity.mouse.click_pos_y-i.y,v=l*l+u*u,p=-o/v*1;v<=o&&function(){var e=Math.atan2(u,l);if(i.vx=p*Math.cos(e),i.vy=p*Math.sin(e),"bounce"==d.particles.move.out_mode){var t=i.x+i.vx,a=i.y+i.vy;t+i.radius>d.canvas.w?i.vx=-i.vx:t-i.radius<0&&(i.vx=-i.vx),a+i.radius>d.canvas.h?i.vy=-i.vy:a-i.radius<0&&(i.vy=-i.vy)}}()}else 0==d.tmp.repulse_clicking&&(i.vx=i.vx_i,i.vy=i.vy_i)},d.fn.modes.grabParticle=function(e){if(d.interactivity.events.onhover.enable&&"mousemove"==d.interactivity.status){var t=e.x-d.interactivity.mouse.pos_x,a=e.y-d.interactivity.mouse.pos_y,i=Math.sqrt(t*t+a*a);if(i<=d.interactivity.modes.grab.distance){var n=d.interactivity.modes.grab.line_linked.opacity-i/(1/d.interactivity.modes.grab.line_linked.opacity)/d.interactivity.modes.grab.distance;if(0<n){var s=d.particles.line_linked.color_rgb_line;d.canvas.ctx.strokeStyle="rgba("+s.r+","+s.g+","+s.b+","+n+")",d.canvas.ctx.lineWidth=d.particles.line_linked.width,d.canvas.ctx.beginPath(),d.canvas.ctx.moveTo(e.x,e.y),d.canvas.ctx.lineTo(d.interactivity.mouse.pos_x,d.interactivity.mouse.pos_y),d.canvas.ctx.stroke(),d.canvas.ctx.closePath()}}}},d.fn.vendors.eventsListeners=function(){"window"==d.interactivity.detect_on?d.interactivity.el=window:d.interactivity.el=d.canvas.el,(d.interactivity.events.onhover.enable||d.interactivity.events.onclick.enable)&&(d.interactivity.el.addEventListener("mousemove",function(e){if(d.interactivity.el==window)var t=e.clientX,a=e.clientY;else t=e.offsetX||e.clientX,a=e.offsetY||e.clientY;d.interactivity.mouse.pos_x=t,d.interactivity.mouse.pos_y=a,d.tmp.retina&&(d.interactivity.mouse.pos_x*=d.canvas.pxratio,d.interactivity.mouse.pos_y*=d.canvas.pxratio),d.interactivity.status="mousemove"}),d.interactivity.el.addEventListener("mouseleave",function(e){d.interactivity.mouse.pos_x=null,d.interactivity.mouse.pos_y=null,d.interactivity.status="mouseleave"})),d.interactivity.events.onclick.enable&&d.interactivity.el.addEventListener("click",function(){if(d.interactivity.mouse.click_pos_x=d.interactivity.mouse.pos_x,d.interactivity.mouse.click_pos_y=d.interactivity.mouse.pos_y,d.interactivity.mouse.click_time=(new Date).getTime(),d.interactivity.events.onclick.enable)switch(d.interactivity.events.onclick.mode){case"push":d.particles.move.enable?d.fn.modes.pushParticles(d.interactivity.modes.push.particles_nb,d.interactivity.mouse):1==d.interactivity.modes.push.particles_nb?d.fn.modes.pushParticles(d.interactivity.modes.push.particles_nb,d.interactivity.mouse):1<d.interactivity.modes.push.particles_nb&&d.fn.modes.pushParticles(d.interactivity.modes.push.particles_nb);break;case"remove":d.fn.modes.removeParticles(d.interactivity.modes.remove.particles_nb);break;case"bubble":d.tmp.bubble_clicking=!0;break;case"repulse":d.tmp.repulse_clicking=!0,d.tmp.repulse_count=0,d.tmp.repulse_finish=!1,setTimeout(function(){d.tmp.repulse_clicking=!1},1e3*d.interactivity.modes.repulse.duration)}})},d.fn.vendors.densityAutoParticles=function(){if(d.particles.number.density.enable){var e=d.canvas.el.width*d.canvas.el.height/1e3;d.tmp.retina&&(e/=2*d.canvas.pxratio);var t=e*d.particles.number.value/d.particles.number.density.value_area,a=d.particles.array.length-t;a<0?d.fn.modes.pushParticles(Math.abs(a)):d.fn.modes.removeParticles(a)}},d.fn.vendors.checkOverlap=function(e,t){for(var a=0;a<d.particles.array.length;a++){var i=d.particles.array[a],n=e.x-i.x,s=e.y-i.y;Math.sqrt(n*n+s*s)<=e.radius+i.radius&&(e.x=t?t.x:Math.random()*d.canvas.w,e.y=t?t.y:Math.random()*d.canvas.h,d.fn.vendors.checkOverlap(e))}},d.fn.vendors.createSvgImg=function(s){var e=d.tmp.source_svg.replace(/#([0-9A-F]{3,6})/gi,function(e,t,a,i){if(s.color.rgb)var n="rgba("+s.color.rgb.r+","+s.color.rgb.g+","+s.color.rgb.b+","+s.opacity+")";else n="hsla("+s.color.hsl.h+","+s.color.hsl.s+"%,"+s.color.hsl.l+"%,"+s.opacity+")";return n}),t=new Blob([e],{type:"image/svg+xml;charset=utf-8"}),a=window.URL||window.webkitURL||window,i=a.createObjectURL(t),n=new Image;n.addEventListener("load",function(){s.img.obj=n,s.img.loaded=!0,a.revokeObjectURL(i),d.tmp.count_svg++}),n.src=i},d.fn.vendors.destroypJS=function(){cancelAnimationFrame(d.fn.drawAnimFrame),a.remove(),pJSDom=null},d.fn.vendors.drawShape=function(e,t,a,i,n,s){var r=n*s,c=n/s,o=180*(c-2)/c,l=Math.PI-Math.PI*o/180;e.save(),e.beginPath(),e.translate(t,a),e.moveTo(0,0);for(var u=0;u<r;u++)e.lineTo(i,0),e.translate(i,0),e.rotate(l);e.fill(),e.restore()},d.fn.vendors.exportImg=function(){window.open(d.canvas.el.toDataURL("image/png"),"_blank")},d.fn.vendors.loadImg=function(e){if(d.tmp.img_error=void 0,""!=d.particles.shape.image.src)if("svg"==e){var t=new XMLHttpRequest;t.open("GET",d.particles.shape.image.src),t.onreadystatechange=function(e){4==t.readyState&&(200==t.status?(d.tmp.source_svg=e.currentTarget.response,d.fn.vendors.checkBeforeDraw()):(console.log("Error pJS - Image not found"),d.tmp.img_error=!0))},t.send()}else{var a=new Image;a.addEventListener("load",function(){d.tmp.img_obj=a,d.fn.vendors.checkBeforeDraw()}),a.src=d.particles.shape.image.src}else console.log("Error pJS - No image.src"),d.tmp.img_error=!0},d.fn.vendors.draw=function(){"image"==d.particles.shape.type?"svg"==d.tmp.img_type?d.tmp.count_svg>=d.particles.number.value?(d.fn.particlesDraw(),d.particles.move.enable?d.fn.drawAnimFrame=requestAnimFrame(d.fn.vendors.draw):cancelRequestAnimFrame(d.fn.drawAnimFrame)):d.tmp.img_error||(d.fn.drawAnimFrame=requestAnimFrame(d.fn.vendors.draw)):null!=d.tmp.img_obj?(d.fn.particlesDraw(),d.particles.move.enable?d.fn.drawAnimFrame=requestAnimFrame(d.fn.vendors.draw):cancelRequestAnimFrame(d.fn.drawAnimFrame)):d.tmp.img_error||(d.fn.drawAnimFrame=requestAnimFrame(d.fn.vendors.draw)):(d.fn.particlesDraw(),d.particles.move.enable?d.fn.drawAnimFrame=requestAnimFrame(d.fn.vendors.draw):cancelRequestAnimFrame(d.fn.drawAnimFrame))},d.fn.vendors.checkBeforeDraw=function(){"image"==d.particles.shape.type?"svg"==d.tmp.img_type&&null==d.tmp.source_svg?d.tmp.checkAnimFrame=requestAnimFrame(check):(cancelRequestAnimFrame(d.tmp.checkAnimFrame),d.tmp.img_error||(d.fn.vendors.init(),d.fn.vendors.draw())):(d.fn.vendors.init(),d.fn.vendors.draw())},d.fn.vendors.init=function(){d.fn.retinaInit(),d.fn.canvasInit(),d.fn.canvasSize(),d.fn.canvasPaint(),d.fn.particlesCreate(),d.fn.vendors.densityAutoParticles(),d.particles.line_linked.color_rgb_line=hexToRgb(d.particles.line_linked.color)},d.fn.vendors.start=function(){isInArray("image",d.particles.shape.type)?(d.tmp.img_type=d.particles.shape.image.src.substr(d.particles.shape.image.src.length-3),d.fn.vendors.loadImg(d.tmp.img_type)):d.fn.vendors.checkBeforeDraw()},d.fn.vendors.eventsListeners(),d.fn.vendors.start()};function hexToRgb(e){e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(e,t,a,i){return t+t+a+a+i+i});var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null}function clamp(e,t,a){return Math.min(Math.max(e,t),a)}function isInArray(e,t){return-1<t.indexOf(e)}Object.deepExtend=function(e,t){for(var a in t)t[a]&&t[a].constructor&&t[a].constructor===Object?(e[a]=e[a]||{},arguments.callee(e[a],t[a])):e[a]=t[a];return e},window.requestAnimFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)},window.cancelRequestAnimFrame=window.cancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||clearTimeout,window.pJSDom=[],window.particlesJS=function(e,t){"string"!=typeof e&&(t=e,e="particles-js"),e||(e="particles-js");var a=document.getElementById(e),i="particles-js-canvas-el",n=a.getElementsByClassName(i);if(n.length)for(;0<n.length;)a.removeChild(n[0]);var s=document.createElement("canvas");s.className=i,s.style.width="100%",s.style.height="100%",null!=document.getElementById(e).appendChild(s)&&pJSDom.push(new pJS(e,t))},window.particlesJS.load=function(a,e,i){var n=new XMLHttpRequest;n.open("GET",e),n.onreadystatechange=function(e){if(4==n.readyState)if(200==n.status){var t=JSON.parse(e.currentTarget.response);window.particlesJS(a,t),i&&i()}else console.log("Error pJS - XMLHttpRequest status: "+n.status),console.log("Error pJS - File config not found")},n.send()},$(document).ready(function(){"use strict";$(".navbar-nav li.dropdown").hover(function(){$(this).find(".dropdown-menu").stop(!0,!0).delay(100).fadeIn(500)},function(){$(this).find(".dropdown-menu").stop(!0,!0).delay(100).fadeOut(500)}),$(window).scroll(function(){100<=$(window).scrollTop()?$(".navbar").addClass("fixed-navbar inner-navbar fixed-top"):$(".navbar").removeClass("fixed-navbar inner-navbar fixed-top")});var e=$(".screens");e&&e.owlCarousel({center:!0,items:2,loop:!0,navText:["<i class='fa fa-chevron-left'></i>","<i class='fa fa-chevron-right'></i>"],responsive:{0:{items:1,nav:!0},800:{items:2,nav:!0}}});var t=$(".blogs-slider");0<t.length&&t.owlCarousel({items:3,responsive:{0:{items:1,nav:!1},800:{items:3,nav:!1}}}),particlesJS("particles-js",{particles:{number:{value:80,density:{enable:!0,value_area:800}},color:{value:"#ffffff"},shape:{type:"circle",stroke:{width:0,color:"#000000"},polygon:{nb_sides:5},image:{src:"img/github.svg",width:100,height:100}},opacity:{value:.5,random:!1,anim:{enable:!1,speed:1,opacity_min:.1,sync:!1}},size:{value:3,random:!0,anim:{enable:!1,speed:40,size_min:.1,sync:!1}},line_linked:{enable:!0,distance:150,color:"#ffffff",opacity:.4,width:1},move:{enable:!0,speed:6,direction:"none",random:!1,straight:!1,out_mode:"out",bounce:!1,attract:{enable:!1,rotateX:600,rotateY:1200}}},interactivity:{detect_on:"canvas",events:{onhover:{enable:!0,mode:"grab"},onclick:{enable:!0,mode:"push"},resize:!0},modes:{grab:{distance:140,line_linked:{opacity:1}},bubble:{distance:400,size:40,duration:2,opacity:8,speed:3},repulse:{distance:200,duration:.4},push:{particles_nb:4},remove:{particles_nb:2}}},retina_detect:!0}),(new WOW).init()}),function(a){"use strict";var e=a(".bg-section"),t=a(".bg-pattern"),i=a(".col-bg");if(e.each(function(){var e="url("+a(this).children("img").attr("src")+")";a(this).parent().css("backgroundImage",e),a(this).parent().addClass("bg-section"),a(this).remove()}),t.each(function(){var e="url("+a(this).children("img").attr("src")+")";a(this).parent().css("backgroundImage",e),a(this).parent().addClass("bg-pattern"),a(this).remove()}),i.each(function(){var e="url("+a(this).children("img").attr("src")+")";a(this).parent().css("backgroundImage",e),a(this).parent().addClass("col-bg"),a(this).remove()}),a(".carousel").each(function(){var e=a(this);e.owlCarousel({loop:e.data("loop"),autoplay:e.data("autoplay"),margin:e.data("space"),nav:e.data("nav"),dots:e.data("dots"),center:e.data("center"),dotsSpeed:e.data("speed"),responsive:{0:{items:1},600:{items:e.data("slide-rs")},1e3:{items:e.data("slide")}}})}),a(".img-popup").magnificPopup({type:"image"}),a(".img-gallery-item").magnificPopup({type:"image",gallery:{enabled:!0}}),a(".popup-video,.popup-gmaps").magnificPopup({disableOn:700,mainClass:"mfp-fade",removalDelay:0,preloader:!1,fixedContentPos:!1,type:"iframe",iframe:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" frameborder="0" allowfullscreen></iframe></div>',patterns:{youtube:{index:"youtube.com/",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"}},srcAction:"iframe_src"}}),a('a[data-scroll="scrollTo"]').on("click",function(e){var t=a(a(this).attr("href"));t.length&&(e.preventDefault(),a("html, body").animate({scrollTop:t.offset().top-100},1e3),a(this).hasClass("menu-item")&&(a(this).parent().addClass("active"),a(this).parent().siblings().removeClass("active")))}),a(".navbar-toggler").on("click",function(){a(this).toggleClass("toggler-active")}),a(0<".pricing-filter".length)){var n=a(".pricing-filter .monthly"),s=a(".pricing-filter .yearly"),r=a(".pricing-switcher");s.on("click",function(e){e.preventDefault(),a(this).toggleClass("is-active"),a(this).siblings().removeClass("is-active"),a(this).siblings(".switch").toggleClass("switch-yearly").removeClass("switch-monthly"),r.toggleClass("active-yearly").removeClass("active-monthly")}),n.on("click",function(e){e.preventDefault(),a(this).toggleClass("is-active"),a(this).siblings().removeClass("is-active"),a(this).siblings(".switch").toggleClass("switch-monthly").removeClass("switch-yearly"),r.toggleClass("active-monthly").removeClass("active-yearly")})}ScrollReveal().reveal(".reveal",{duration:1e3,delay:300})}(jQuery);