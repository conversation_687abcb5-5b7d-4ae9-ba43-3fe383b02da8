/* Modern Theme Styles */
:root {
    --primary-color: #4285f4;
    --text-color: #1a1f36;
    --bg-color: #ffffff;
    --secondary-bg: #f7fafc;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styles */
.header {
    padding: 1rem 0;
    background: var(--bg-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 0.5rem 1rem;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    background: transparent;
    display: none;
}

.navbar-toggler-icon {
    display: block;
    width: 24px;
    height: 2px;
    background: var(--text-color);
    position: relative;
    transition: background 0.3s ease;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--text-color);
    transition: transform 0.3s ease;
}

.navbar-toggler-icon::before {
    top: -6px;
}

.navbar-toggler-icon::after {
    bottom: -6px;
}

.navbar-collapse.show .navbar-toggler-icon {
    background: transparent;
}

.navbar-collapse.show .navbar-toggler-icon::before {
    transform: rotate(45deg) translate(4px, 4px);
}

.navbar-collapse.show .navbar-toggler-icon::after {
    transform: rotate(-45deg) translate(4px, -4px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    text-decoration: none;
}

.nav-link {
    color: var(--text-color);
    margin: 0 1rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 6px;
    border: none;
    transition: transform 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

/* Hero Section */
.hero-area {
    padding: 8rem 0 4rem;
    background: var(--secondary-bg);
    position: relative;
    overflow: hidden;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.shape-blur {
    position: absolute;
    top: -50%;
    right: -20%;
    width: 60%;
    height: 150%;
    background: radial-gradient(circle, rgba(66,133,244,0.1) 0%, rgba(66,133,244,0) 70%);
    transform: rotate(-45deg);
}

.shape-gradient {
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 50%;
    height: 100%;
    background: linear-gradient(45deg, rgba(66,133,244,0.1) 0%, rgba(66,133,244,0) 100%);
    transform: rotate(30deg);
}

.hero-text {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #4a5568;
    margin-bottom: 2rem;
}

/* URL Shortener Form */
.url-shortener {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.url-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.url-input:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Responsive Design */
@media (max-width: 991px) {
    .navbar-toggler {
        display: block;
    }

    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-color);
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        display: none;
    }

    .navbar-collapse.show {
        display: block;
    }

    .navbar-nav {
        flex-direction: column;
        width: 100%;
        text-align: center;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        margin: 0;
        display: block;
    }

    .nav-link.btn-primary {
        margin-top: 0.5rem;
        display: inline-block;
    }

    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
}
