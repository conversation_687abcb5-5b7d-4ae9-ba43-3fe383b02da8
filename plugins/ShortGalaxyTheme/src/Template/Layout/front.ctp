<?php
/**
 * @var \App\View\AppView $this
 */
?>
<!DOCTYPE html>
<html lang="<?= locale_get_primary_language(null) ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h($this->fetch('title')); ?></title>
    <meta name="description" content="<?= h($this->fetch('description')); ?>">
    <meta name="keywords" content="<?= h(get_option('seo_keywords')); ?>">
    <?= $this->Assets->favicon() ?>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4285f4',
                        secondary: '#1a1f36',
                        'light-blue': '#f7fafc'
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style type="text/css">
        /* Override any conflicting styles */
        .md\:flex { display: flex !important; }
        .md\:hidden { display: none !important; }
        @media (max-width: 768px) {
            .md\:flex { display: none !important; }
            .md\:hidden { display: block !important; }
        }
    </style>
</head>

<body class="<?= ($this->request->getParam('_name') === 'home') ? 'home' : 'inner-page' ?>">
<?= get_option('after_body_tag_code'); ?>
<!-- Modern Header -->
<header class="fixed top-0 left-0 right-0 bg-white shadow-md z-50">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-[70px]">
            <div class="flex items-center">
                <a href="<?= build_main_domain_url('/'); ?>" class="flex items-center">
                    <span class="text-3xl font-bold">
                        <?php
                        $siteName = h(get_option('site_name'));
                        $chars = str_split($siteName);
                        foreach ($chars as $char) {
                            if (ctype_upper($char)) {
                                echo "<span class=\"text-blue-500\">{$char}</span>";
                            } else {
                                echo "<span class=\"text-gray-800\">{$char}</span>";
                            }
                        }
                        ?>
                    </span>
                </a>
            </div>

            <!-- Desktop Menu -->
            <nav class="hidden md:flex items-center space-x-6">
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/'); ?>">Home</a>
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/pages/payout-rates'); ?>">Publisher Rates</a>
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/pages/payment-proofs'); ?>">Payment Proofs</a>
                <a class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition" href="<?= build_main_domain_url('/auth/signin'); ?>">Register</a>
            </nav>

            <!-- Mobile Toggle Button -->
            <div class="md:hidden">
                <button id="menu-button" class="text-gray-500 focus:outline-none">
                    <i class="fas fa-bars text-blue-700 text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Dropdown Mobile Menu Fixed Below Navbar -->
    <div id="dropdown-menu" class="hidden md:hidden fixed top-[70px] left-0 right-0 bg-white shadow-md z-40">
        <div class="container mx-auto px-4">
            <nav class="flex flex-col items-start space-y-4 py-4">
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/'); ?>">Home</a>
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/pages/payout-rates'); ?>">Publisher Rates</a>
                <a class="text-gray-500 hover:text-gray-800 transition" href="<?= build_main_domain_url('/pages/payment-proofs'); ?>">Payment Proofs</a>
                <a class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition" href="<?= build_main_domain_url('/auth/signin'); ?>">Register</a>
            </nav>
        </div>
    </div>
</header>

<!-- Mobile Menu Toggle Script -->
<script>
document.getElementById('menu-button')?.addEventListener('click', function() {
    const dropdownMenu = document.getElementById('dropdown-menu');
    dropdownMenu?.classList.toggle('hidden');
});
</script>

<!-- Add padding to body to prevent content from hiding under fixed navbar -->
<div class="pt-[70px]">
    <?= $this->Flash->render() ?>
    <?= $this->fetch('content') ?>
</div>

<?= $this->element('front_footer'); ?>

</body>
</html>
